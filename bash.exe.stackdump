Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x2118E
0007FFFFBBB0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x69BA
0007FFFFBBB0  0002100469F2 (00021028DF99, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBB0  00021006A545 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBE90  00021006B9A5 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF119F0000 ntdll.dll
7FFF10240000 KERNEL32.DLL
7FFF0F190000 KERNELBASE.dll
7FFF10FE0000 USER32.dll
7FFF0F080000 win32u.dll
000210040000 msys-2.0.dll
7FFF10F00000 GDI32.dll
7FFF0F570000 gdi32full.dll
7FFF0F790000 msvcp_win.dll
7FFF0F690000 ucrtbase.dll
7FFF10D10000 advapi32.dll
7FFF11180000 msvcrt.dll
7FFF10640000 sechost.dll
7FFF10310000 RPCRT4.dll
7FFF0F160000 bcrypt.dll
7FFF0EA00000 CRYPTBASE.DLL
7FFF0F4E0000 bcryptPrimitives.dll
7FFF10610000 IMM32.DLL
