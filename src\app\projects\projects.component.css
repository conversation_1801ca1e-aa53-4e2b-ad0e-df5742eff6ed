/* Custom animations for smooth interactions */
.project-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
              0 0 0 1px rgba(236, 72, 153, 0.1);
}

/* Smooth image scaling */
.project-image {
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.project-card:hover .project-image {
  transform: scale(1.1);
}

/* Skill tag animations */
.skill-tag {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.skill-tag:hover {
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 4px 8px rgba(236, 72, 153, 0.2);
}

/* Button hover effects */
.project-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.project-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.project-button:hover::before {
  left: 100%;
}

.project-button:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 8px 16px rgba(236, 72, 153, 0.3);
}

/* Title underline animation */
.title-underline {
  position: relative;
  overflow: hidden;
}

.title-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #ec4899, #f472b6);
  transition: width 0.3s ease-out;
}

.project-card:hover .title-underline::after {
  width: 100%;
}

/* Smooth loading animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Stagger delay for cards */
.project-card:nth-child(1) { animation-delay: 0.1s; }
.project-card:nth-child(2) { animation-delay: 0.2s; }
.project-card:nth-child(3) { animation-delay: 0.3s; }
.project-card:nth-child(4) { animation-delay: 0.4s; }
.project-card:nth-child(5) { animation-delay: 0.5s; }
.project-card:nth-child(6) { animation-delay: 0.6s; }
