<nav class="h-[70px] sticky top-0 z-50 border-b-2 border-pink-300 theme-bg-gradient px-8 flex items-center justify-between">
  <!-- Menu toggle for mobile -->
  <input type="checkbox" id="check" class="hidden peer" />
  <label for="check" class="menu block lg:hidden cursor-pointer z-50">
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-list">
      <path fill-rule="evenodd"
        d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z" />
    </svg>
  </label>

  <!-- Logo -->
  <div class="logo">
    <h2 class="theme-text-primary font-bold cursor-pointer text-xl">RA</h2>
  </div>

  <!-- Navigation Links -->
  <div
    class="nav-items peer-checked:right-0 fixed lg:static top-0 right-[-250px] h-screen lg:h-auto w-[250px] lg:w-auto flex flex-col lg:flex-row justify-evenly lg:justify-end items-start lg:items-center bg-pink-400 lg:bg-transparent transition-all duration-500 p-8 lg:p-0 gap-y-6 lg:gap-x-6">
    <ul class="flex flex-col lg:flex-row gap-y-4 lg:gap-x-4 theme-text-secondary text-[18px] font-medium">
      <li><a routerLink="/" class="hover:text-pink-700 relative after:block after:h-[3px] after:bg-pink-600 after:w-0 hover:after:w-full after:transition-all after:duration-300">Home</a></li>
      <li><a routerLink="/about" class="hover:text-pink-700 relative after:block after:h-[3px] after:bg-pink-600 after:w-0 hover:after:w-full after:transition-all after:duration-300">About</a></li>
      <li><a href="#skills" class="hover:text-pink-700 relative after:block after:h-[3px] after:bg-pink-600 after:w-0 hover:after:w-full after:transition-all after:duration-300">Skills</a></li>
      <li><a href="#projects" class="hover:text-pink-700 relative after:block after:h-[3px] after:bg-pink-600 after:w-0 hover:after:w-full after:transition-all after:duration-300">Projects</a></li>
      <li><a href="#contact" class="hover:text-pink-700 relative after:block after:h-[3px] after:bg-pink-600 after:w-0 hover:after:w-full after:transition-all after:duration-300">Contact</a></li>
    </ul>
  </div>
</nav>
