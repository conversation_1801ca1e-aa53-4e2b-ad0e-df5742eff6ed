/* Modern About Component Styles - Converted to Tailwind CSS */
/* Most styles moved to template classes. Only custom gradients and pseudo-elements remain. */

/* Custom gradient background that can't be replicated with Tailwind */
.about-me-gradient-bg {
  background: linear-gradient(135deg, #fdf2f8 0%, rgba(255, 255, 255, 0.95) 50%, #ffe4e6 100%);
}

/* Title gradient text effect */
.about-me-title-gradient {
  background: linear-gradient(135deg, #db2777, #f472b6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

/* Title underline decoration */
.about-me-title-gradient::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #db2777, #f472b6);
  border-radius: 2px;
}
/* Content container with backdrop blur effect */
.about-me-content-blur {
  background: rgba(255, 255, 255, 0.7);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(219, 39, 119, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* CTA Button with custom gradient and animations */
.cta-gradient {
  background: linear-gradient(135deg, #db2777, #f472b6);
  box-shadow:
    0 8px 25px rgba(219, 39, 119, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.cta-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cta-gradient:hover {
  background: linear-gradient(135deg, #f472b6, #db2777);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(219, 39, 119, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.cta-gradient:hover::before {
  left: 100%;
}

.cta-gradient:active {
  transform: translateY(-1px) scale(0.98);
}

/* CTA icon animation */
.cta-icon {
  transform: translateX(-3px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cta-gradient:hover .cta-icon {
  transform: translateX(3px);
  stroke-width: 3;
}

/* Spline iframe custom border radius */
.spline-iframe-rounded {
  border-radius: 0 20px 20px 0;
}

/* Accessibility: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .cta-gradient,
  .cta-gradient::before,
  .cta-icon,
  .about-me-title-gradient {
    transition: none;
  }
}
