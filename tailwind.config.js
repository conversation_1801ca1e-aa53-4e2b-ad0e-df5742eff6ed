/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}"
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'sans-serif'],
        'primary': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'sans-serif'],
        'montserrat': ['Montserrat', 'sans-serif'],
        'nunito': ['Nunito', 'sans-serif'],
        'pacifico': ['Pacifico', 'cursive'],
        'ubuntu': ['Ubuntu', 'sans-serif'],
      },
      colors: {
        'primary-pink': {
          DEFAULT: '#db2777', // pink-600
          'dark': '#be185d', // pink-700
          'light': '#f9a8d4', // pink-300
          'lighter': '#fce7f3', // pink-100
          'lightest': '#fdf2f8', // pink-50
        },
        'accent-pink': '#f472b6', // pink-400
        'border-pink': {
          DEFAULT: '#fbcfe8', // pink-200
          'dark': '#f9a8d4', // pink-300
        },
        'text-primary': '#db2777',
        'text-secondary': '#be185d',
      },
      spacing: {
        '18': '4.5rem',
        '25': '6.25rem',
      },
      scale: {
        '200': '2',
      },
      zIndex: {
        '-10': '-10',
        '-3': '-3',
      },
      margin: {
        'clamp-responsive': 'clamp(20px, 10vw, 150px)',
      },
      maxWidth: {
        '1300': '1300px',
        '1250': '1250px',
      },
      boxShadow: {
        'pink': '0px 7px 50px 0px #f9a8d4',
        'card': '0px 0px 20px rgba(0, 0, 0, 0.055)',
        'drop': '0 0 25px rgb(0, 0, 0)',
      },
      animation: {
        'slide-in-top': 'slide-in-top 0.3s both',
      },
      keyframes: {
        'slide-in-top': {
          '0%': {
            transform: 'translateY(-50px)',
            opacity: '0',
          },
          '100%': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
      },
    },
  },
  plugins: [],
}
