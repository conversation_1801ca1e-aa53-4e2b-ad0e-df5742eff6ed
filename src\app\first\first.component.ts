import { Component, OnInit, On<PERSON><PERSON>roy, ElementRef } from '@angular/core';

declare var VANTA: any;

@Component({
  selector: 'app-first',
  templateUrl: './first.component.html',
  styleUrl: './first.component.css'
})
export class FirstComponent implements OnInit, OnDestroy {
  private vantaEffect: any;
  private originalSettings = {
    speedLimit: 9.00,
    separation: 58.00,
    cohesion: 40.00
  };
  private hoverSettings = {
    speedLimit: 25.00,
  separation: 250.00, // زودي القيمة هنا مثلاً من 120 لـ 250
  cohesion: 10.00      // قللي الكوهيجن علشان يحسوا إنهم مش بيلموا على بعض
  };

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    this.vantaEffect = VANTA.BIRDS({
      el: "#vanta-birds",
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      backgroundColor: 0xae179c,
      color1: 0xa314a4,
      color2: 0xeae612,
      birdSize: 1.20,
      wingSpan: 32.00,
      speedLimit: this.originalSettings.speedLimit,
      separation: this.originalSettings.separation,
      alignment: 77.00,
      cohesion: this.originalSettings.cohesion,
      quantity: 4.00,
      backgroundAlpha: 0.35
    });

    this.setupHoverEffects();
  }

  private setupHoverEffects() {
    const heroSection = this.elementRef.nativeElement.querySelector('#vanta-birds');

    if (heroSection) {
      heroSection.addEventListener('mouseenter', () => {
        this.onHoverStart();
      });

      heroSection.addEventListener('mouseleave', () => {
        this.onHoverEnd();
      });
    }
  }

  private onHoverStart() {
    if (this.vantaEffect) {
      this.vantaEffect.setOptions({
        speedLimit: this.hoverSettings.speedLimit,
        separation: this.hoverSettings.separation,
        cohesion: this.hoverSettings.cohesion
      });
    }
  }

  private onHoverEnd() {
    if (this.vantaEffect) {
      this.vantaEffect.setOptions({
        speedLimit: this.originalSettings.speedLimit,
        separation: this.originalSettings.separation,
        cohesion: this.originalSettings.cohesion
      });
    }
  }

  ngOnDestroy() {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  }
}
