<section class="relative overflow-hidden min-h-screen about-me-gradient-bg scroll-smooth" id="about-me">
  <!-- Header title at the top middle -->
  <div class="relative z-10 pt-10 pb-8 px-0 text-center md:pt-8 md:pb-5 sm:pt-6 sm:pb-4">
    <div class="text-6xl font-extrabold leading-tight m-0 relative about-me-title-gradient md:text-5xl sm:text-4xl">
      About
    </div>
  </div>

  <div class="flex flex-row min-h-screen items-center relative lg:flex-col lg:min-h-screen">
    <!-- Spline animation on the left -->
    <div class="w-1/2 h-screen relative z-[1] lg:w-full lg:h-96 md:h-80 sm:h-72">
      <iframe
        src="https://my.spline.design/molang3dcopy-WdvJb5OqkYqVTP1prRjNjqcv/"
        frameborder="0"
        class="w-full h-full border-none block spline-iframe-rounded lg:rounded-none"
        allowfullscreen>
      </iframe>
    </div>

    <!-- Content on the right -->
    <div class="w-1/2 p-10 flex items-center justify-end min-h-screen z-[2] lg:w-full lg:p-8 lg:min-h-[60vh] md:p-6 md:min-h-[50vh] sm:p-4 sm:min-h-[40vh]">
      <div class="relative z-[2] pb-0 lg:pb-15">
        <div class="flex flex-col items-end gap-10 text-right p-5 md:gap-8 md:p-4 sm:gap-6 sm:p-3">
          <div class="flex flex-col items-end gap-10 w-full max-w-4xl rounded-3xl p-10 border border-border-pink about-me-content-blur md:gap-8 md:max-w-full md:p-8 md:rounded-2xl sm:p-5 sm:gap-6 sm:rounded-xl">
            <div class="text-text-secondary font-medium text-xl leading-relaxed text-right max-w-full m-0 relative md:text-lg md:leading-normal sm:text-base sm:leading-normal">
              {{ about.description || 'Loading description...' }}
            </div>

            <a href="https://drive.google.com/file/d/1So0fhL4n2hvNY3CpCJktXAbe1ZYJg8be/view?usp=sharing" target="_blank">
              <button type="button" class="relative inline-flex items-center py-5 px-10 border-none rounded-full cursor-pointer transition-all duration-500 ease-out text-decoration-none overflow-hidden cta-gradient md:py-4 md:px-9 sm:py-4 sm:px-8">
                <span class="relative text-lg font-semibold text-white z-[2] md:text-base sm:text-sm">Resume</span>
                <svg width="15px" height="10px" viewBox="0 0 13 10" class="relative ml-3 fill-none stroke-white stroke-2 z-[2] cta-icon">
                  <path d="M1,5 L11,5"></path>
                  <polyline points="8 1 12 5 8 9"></polyline>
                </svg>
              </button>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

