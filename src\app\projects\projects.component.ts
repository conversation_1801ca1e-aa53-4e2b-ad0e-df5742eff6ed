import { Component, OnInit } from '@angular/core';
import { trigger, state, style, transition, animate, query, stagger } from '@angular/animations';
import { ApiService } from '../project.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.css'],
  animations: [
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('600ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('staggerAnimation', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(30px)' }),
          stagger(100, [
            animate('600ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('cardHover', [
      state('normal', style({ transform: 'scale(1)' })),
      state('hovered', style({ transform: 'scale(1.02)' })),
      transition('normal <=> hovered', animate('300ms ease-in-out'))
    ])
  ]
})
export class ProjectsComponent implements OnInit {
  projects: any[] = [];
  hoveredCard: number | null = null;

  constructor(private apiService: ApiService) { }

  ngOnInit(): void {
    this.apiService.getProjects().subscribe(
      (data: any[]) => {
        this.projects = data.map(project => ({
  photo: project.photo,
  name: project.name,
  title: project.title,
  description: project.description,
  link: project.link,
  githubLink: project.githubLink,
  skills: project.skills
}));

      },
      (error) => {
        console.error('Error fetching projects:', error);
      }
    );
  }

  openGitHub(link: string): void {
    window.open(link, '_blank');
  }

  onCardHover(index: number): void {
    this.hoveredCard = index;
  }

  onCardLeave(): void {
    this.hoveredCard = null;
  }

  getCardState(index: number): string {
    return this.hoveredCard === index ? 'hovered' : 'normal';
  }
}
