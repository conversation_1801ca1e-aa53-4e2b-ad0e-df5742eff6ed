var Ay=Object.defineProperty,Ty=Object.defineProperties;var xy=Object.getOwnPropertyDescriptors;var bo=Object.getOwnPropertySymbols;var Bf=Object.prototype.hasOwnProperty,$f=Object.prototype.propertyIsEnumerable;var jf=(e,t,n)=>t in e?Ay(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,E=(e,t)=>{for(var n in t||={})Bf.call(t,n)&&jf(e,n,t[n]);if(bo)for(var n of bo(t))$f.call(t,n)&&jf(e,n,t[n]);return e},Z=(e,t)=>Ty(e,xy(t));var Al=(e,t)=>{var n={};for(var r in e)Bf.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&bo)for(var r of bo(e))t.indexOf(r)<0&&$f.call(e,r)&&(n[r]=e[r]);return n};var _o=(e,t,n)=>new Promise((r,i)=>{var o=l=>{try{a(n.next(l))}catch(c){i(c)}},s=l=>{try{a(n.throw(l))}catch(c){i(c)}},a=l=>l.done?r(l.value):Promise.resolve(l.value).then(o,s);a((n=n.apply(e,t)).next())});function Hf(e,t){return Object.is(e,t)}var ve=null,Io=!1,Mo=1,an=Symbol("SIGNAL");function K(e){let t=ve;return ve=e,t}function zf(){return ve}var di={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Fl(e){if(Io)throw new Error("");if(ve===null)return;ve.consumerOnSignalRead(e);let t=ve.nextProducerIndex++;if(xo(ve),t<ve.producerNode.length&&ve.producerNode[t]!==e&&ui(ve)){let n=ve.producerNode[t];To(n,ve.producerIndexOfThis[t])}ve.producerNode[t]!==e&&(ve.producerNode[t]=e,ve.producerIndexOfThis[t]=ui(ve)?Zf(e,ve,t):0),ve.producerLastReadVersion[t]=e.version}function Fy(){Mo++}function Gf(e){if(!(ui(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Mo)){if(!e.producerMustRecompute(e)&&!Rl(e)){e.dirty=!1,e.lastCleanEpoch=Mo;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Mo}}function Wf(e){if(e.liveConsumerNode===void 0)return;let t=Io;Io=!0;try{for(let n of e.liveConsumerNode)n.dirty||Ny(n)}finally{Io=t}}function qf(){return ve?.consumerAllowSignalWrites!==!1}function Ny(e){e.dirty=!0,Wf(e),e.consumerMarkedDirty?.(e)}function Ao(e){return e&&(e.nextProducerIndex=0),K(e)}function Nl(e,t){if(K(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(ui(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)To(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Rl(e){xo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Gf(n),r!==n.version))return!0}return!1}function Ol(e){if(xo(e),ui(e))for(let t=0;t<e.producerNode.length;t++)To(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Zf(e,t,n){if(Kf(e),e.liveConsumerNode.length===0&&Qf(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Zf(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function To(e,t){if(Kf(e),e.liveConsumerNode.length===1&&Qf(e))for(let r=0;r<e.producerNode.length;r++)To(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];xo(i),i.producerIndexOfThis[r]=t}}function ui(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function xo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Kf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Qf(e){return e.producerNode!==void 0}function Yf(e){let t=Object.create(Ry);t.computation=e;let n=()=>{if(Gf(t),Fl(t),t.value===So)throw t.error;return t.value};return n[an]=t,n}var Tl=Symbol("UNSET"),xl=Symbol("COMPUTING"),So=Symbol("ERRORED"),Ry=Z(E({},di),{value:Tl,dirty:!0,error:null,equal:Hf,producerMustRecompute(e){return e.value===Tl||e.value===xl},producerRecomputeValue(e){if(e.value===xl)throw new Error("Detected cycle in computations.");let t=e.value;e.value=xl;let n=Ao(e),r;try{r=e.computation()}catch(i){r=So,e.error=i}finally{Nl(e,n)}if(t!==Tl&&t!==So&&r!==So&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function Oy(){throw new Error}var Xf=Oy;function Jf(){Xf()}function eh(e){Xf=e}var Uy=null;function th(e){let t=Object.create(rh);t.value=e;let n=()=>(Fl(t),t.value);return n[an]=t,n}function Ul(e,t){qf()||Jf(),e.equal(e.value,t)||(e.value=t,Py(e))}function nh(e,t){qf()||Jf(),Ul(e,t(e.value))}var rh=Z(E({},di),{equal:Hf,value:void 0});function Py(e){e.version++,Fy(),Wf(e),Uy?.()}function U(e){return typeof e=="function"}function ar(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Fo=ar(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function fi(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var fe=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let o of n)o.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(U(r))try{r()}catch(o){t=o instanceof Fo?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{ih(o)}catch(s){t=t??[],s instanceof Fo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Fo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ih(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&fi(n,t)}remove(t){let{_finalizers:n}=this;n&&fi(n,t),t instanceof e&&t._removeParent(this)}};fe.EMPTY=(()=>{let e=new fe;return e.closed=!0,e})();var Pl=fe.EMPTY;function No(e){return e instanceof fe||e&&"closed"in e&&U(e.remove)&&U(e.add)&&U(e.unsubscribe)}function ih(e){U(e)?e():e.unsubscribe()}var dt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var lr={setTimeout(e,t,...n){let{delegate:r}=lr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=lr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Ro(e){lr.setTimeout(()=>{let{onUnhandledError:t}=dt;if(t)t(e);else throw e})}function hi(){}var oh=kl("C",void 0,void 0);function sh(e){return kl("E",void 0,e)}function ah(e){return kl("N",e,void 0)}function kl(e,t,n){return{kind:e,value:t,error:n}}var On=null;function cr(e){if(dt.useDeprecatedSynchronousErrorHandling){let t=!On;if(t&&(On={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=On;if(On=null,n)throw r}}else e()}function lh(e){dt.useDeprecatedSynchronousErrorHandling&&On&&(On.errorThrown=!0,On.error=e)}var Un=class extends fe{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,No(t)&&t.add(this)):this.destination=Vy}static create(t,n,r){return new ur(t,n,r)}next(t){this.isStopped?Vl(ah(t),this):this._next(t)}error(t){this.isStopped?Vl(sh(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Vl(oh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ky=Function.prototype.bind;function Ll(e,t){return ky.call(e,t)}var jl=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Oo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Oo(r)}else Oo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Oo(n)}}},ur=class extends Un{constructor(t,n,r){super();let i;if(U(t)||!t)i={next:t??void 0,error:n??void 0,complete:r??void 0};else{let o;this&&dt.useDeprecatedNextContext?(o=Object.create(t),o.unsubscribe=()=>this.unsubscribe(),i={next:t.next&&Ll(t.next,o),error:t.error&&Ll(t.error,o),complete:t.complete&&Ll(t.complete,o)}):i=t}this.destination=new jl(i)}};function Oo(e){dt.useDeprecatedSynchronousErrorHandling?lh(e):Ro(e)}function Ly(e){throw e}function Vl(e,t){let{onStoppedNotification:n}=dt;n&&lr.setTimeout(()=>n(e,t))}var Vy={closed:!0,next:hi,error:Ly,complete:hi};var dr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ze(e){return e}function Bl(...e){return $l(e)}function $l(e){return e.length===0?ze:e.length===1?e[0]:function(n){return e.reduce((r,i)=>i(r),n)}}var z=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,i){let o=By(n)?n:new ur(n,r,i);return cr(()=>{let{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ch(r),new r((i,o)=>{let s=new ur({next:a=>{try{n(a)}catch(l){o(l),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[dr](){return this}pipe(...n){return $l(n)(this)}toPromise(n){return n=ch(n),new n((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return e.create=t=>new e(t),e})();function ch(e){var t;return(t=e??dt.Promise)!==null&&t!==void 0?t:Promise}function jy(e){return e&&U(e.next)&&U(e.error)&&U(e.complete)}function By(e){return e&&e instanceof Un||jy(e)&&No(e)}function Hl(e){return U(e?.lift)}function G(e){return t=>{if(Hl(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function B(e,t,n,r,i){return new zl(e,t,n,r,i)}var zl=class extends Un{constructor(t,n,r,i,o,s){super(t),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=i?function(a){try{i(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function fr(){return G((e,t)=>{let n=null;e._refCount++;let r=B(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let i=e._connection,o=n;n=null,i&&(!o||i===o)&&i.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var hr=class extends z{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Hl(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new fe;let n=this.getSubject();t.add(this.source.subscribe(B(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=fe.EMPTY)}return t}refCount(){return fr()(this)}};var uh=ar(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ie=(()=>{class e extends z{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Uo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new uh}next(n){cr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){cr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){cr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Pl:(this.currentObservers=null,o.push(n),new fe(()=>{this.currentObservers=null,fi(o,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:i,isStopped:o}=this;r?n.error(i):o&&n.complete()}asObservable(){let n=new z;return n.source=this,n}}return e.create=(t,n)=>new Uo(t,n),e})(),Uo=class extends Ie{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Pl}};var Ae=class extends Ie{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ge=new z(e=>e.complete());function dh(e){return e&&U(e.schedule)}function fh(e){return e[e.length-1]}function Po(e){return U(fh(e))?e.pop():void 0}function ln(e){return dh(fh(e))?e.pop():void 0}function ph(e,t,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?o(u.value):i(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}function hh(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Pn(e){return this instanceof Pn?(this.v=e,this):new Pn(e)}function gh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,o=[];return i={},a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(i[f]=function(y){return new Promise(function(D,S){o.push([f,y,D,S])>1||l(f,y)})},m&&(i[f]=m(i[f])))}function l(f,m){try{c(r[f](m))}catch(y){g(o[0][3],y)}}function c(f){f.value instanceof Pn?Promise.resolve(f.value.v).then(u,d):g(o[0][2],f)}function u(f){l("next",f)}function d(f){l("throw",f)}function g(f,m){f(m),o.shift(),o.length&&l(o[0][0],o[0][1])}}function mh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof hh=="function"?hh(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(o){n[o]=e[o]&&function(s){return new Promise(function(a,l){s=e[o](s),i(a,l,s.done,s.value)})}}function i(o,s,a,l){Promise.resolve(l).then(function(c){o({value:c,done:a})},s)}}var ko=e=>e&&typeof e.length=="number"&&typeof e!="function";function Lo(e){return U(e?.then)}function Vo(e){return U(e[dr])}function jo(e){return Symbol.asyncIterator&&U(e?.[Symbol.asyncIterator])}function Bo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function $y(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var $o=$y();function Ho(e){return U(e?.[$o])}function zo(e){return gh(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:i}=yield Pn(n.read());if(i)return yield Pn(void 0);yield yield Pn(r)}}finally{n.releaseLock()}})}function Go(e){return U(e?.getReader)}function he(e){if(e instanceof z)return e;if(e!=null){if(Vo(e))return Hy(e);if(ko(e))return zy(e);if(Lo(e))return Gy(e);if(jo(e))return yh(e);if(Ho(e))return Wy(e);if(Go(e))return qy(e)}throw Bo(e)}function Hy(e){return new z(t=>{let n=e[dr]();if(U(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function zy(e){return new z(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Gy(e){return new z(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Ro)})}function Wy(e){return new z(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function yh(e){return new z(t=>{Zy(e,t).catch(n=>t.error(n))})}function qy(e){return yh(zo(e))}function Zy(e,t){var n,r,i,o;return ph(this,void 0,void 0,function*(){try{for(n=mh(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}t.complete()})}function ke(e,t,n,r=0,i=!1){let o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Wo(e,t=0){return G((n,r)=>{n.subscribe(B(r,i=>ke(r,e,()=>r.next(i),t),()=>ke(r,e,()=>r.complete(),t),i=>ke(r,e,()=>r.error(i),t)))})}function qo(e,t=0){return G((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function vh(e,t){return he(e).pipe(qo(t),Wo(t))}function Ch(e,t){return he(e).pipe(qo(t),Wo(t))}function wh(e,t){return new z(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Dh(e,t){return new z(n=>{let r;return ke(n,t,()=>{r=e[$o](),ke(n,t,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){n.error(s);return}o?n.complete():n.next(i)},0,!0)}),()=>U(r?.return)&&r.return()})}function Zo(e,t){if(!e)throw new Error("Iterable cannot be null");return new z(n=>{ke(n,t,()=>{let r=e[Symbol.asyncIterator]();ke(n,t,()=>{r.next().then(i=>{i.done?n.complete():n.next(i.value)})},0,!0)})})}function Eh(e,t){return Zo(zo(e),t)}function bh(e,t){if(e!=null){if(Vo(e))return vh(e,t);if(ko(e))return wh(e,t);if(Lo(e))return Ch(e,t);if(jo(e))return Zo(e,t);if(Ho(e))return Dh(e,t);if(Go(e))return Eh(e,t)}throw Bo(e)}function ae(e,t){return t?bh(e,t):he(e)}function F(...e){let t=ln(e);return ae(e,t)}function pr(e,t){let n=U(e)?e:()=>e,r=i=>i.error(n());return new z(t?i=>t.schedule(r,0,i):r)}function Gl(e){return!!e&&(e instanceof z||U(e.lift)&&U(e.subscribe))}var Pt=ar(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function P(e,t){return G((n,r)=>{let i=0;n.subscribe(B(r,o=>{r.next(e.call(t,o,i++))}))})}var{isArray:Ky}=Array;function Qy(e,t){return Ky(t)?e(...t):e(t)}function Ko(e){return P(t=>Qy(e,t))}var{isArray:Yy}=Array,{getPrototypeOf:Xy,prototype:Jy,keys:ev}=Object;function Qo(e){if(e.length===1){let t=e[0];if(Yy(t))return{args:t,keys:null};if(tv(t)){let n=ev(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function tv(e){return e&&typeof e=="object"&&Xy(e)===Jy}function Yo(e,t){return e.reduce((n,r,i)=>(n[r]=t[i],n),{})}function pi(...e){let t=ln(e),n=Po(e),{args:r,keys:i}=Qo(e);if(r.length===0)return ae([],t);let o=new z(nv(r,t,i?s=>Yo(i,s):ze));return n?o.pipe(Ko(n)):o}function nv(e,t,n=ze){return r=>{_h(t,()=>{let{length:i}=e,o=new Array(i),s=i,a=i;for(let l=0;l<i;l++)_h(t,()=>{let c=ae(e[l],t),u=!1;c.subscribe(B(r,d=>{o[l]=d,u||(u=!0,a--),a||r.next(n(o.slice()))},()=>{--s||r.complete()}))},r)},r)}}function _h(e,t,n){e?ke(n,e,t):t()}function Ih(e,t,n,r,i,o,s,a){let l=[],c=0,u=0,d=!1,g=()=>{d&&!l.length&&!c&&t.complete()},f=y=>c<r?m(y):l.push(y),m=y=>{o&&t.next(y),c++;let D=!1;he(n(y,u++)).subscribe(B(t,S=>{i?.(S),o?f(S):t.next(S)},()=>{D=!0},void 0,()=>{if(D)try{for(c--;l.length&&c<r;){let S=l.shift();s?ke(t,s,()=>m(S)):m(S)}g()}catch(S){t.error(S)}}))};return e.subscribe(B(t,f,()=>{d=!0,g()})),()=>{a?.()}}function pe(e,t,n=1/0){return U(t)?pe((r,i)=>P((o,s)=>t(r,o,i,s))(he(e(r,i))),n):(typeof t=="number"&&(n=t),G((r,i)=>Ih(r,i,e,n)))}function gr(e=1/0){return pe(ze,e)}function Mh(){return gr(1)}function mr(...e){return Mh()(ae(e,ln(e)))}function Xo(e){return new z(t=>{he(e()).subscribe(t)})}function Wl(...e){let t=Po(e),{args:n,keys:r}=Qo(e),i=new z(o=>{let{length:s}=n;if(!s){o.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let d=!1;he(n[u]).subscribe(B(o,g=>{d||(d=!0,c--),a[u]=g},()=>l--,void 0,()=>{(!l||!d)&&(c||o.next(r?Yo(r,a):a),o.complete())}))}});return t?i.pipe(Ko(t)):i}function Le(e,t){return G((n,r)=>{let i=0;n.subscribe(B(r,o=>e.call(t,o,i++)&&r.next(o)))})}function cn(e){return G((t,n)=>{let r=null,i=!1,o;r=t.subscribe(B(n,void 0,void 0,s=>{o=he(e(s,cn(e)(t))),r?(r.unsubscribe(),r=null,o.subscribe(n)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(n))})}function Sh(e,t,n,r,i){return(o,s)=>{let a=n,l=t,c=0;o.subscribe(B(s,u=>{let d=c++;l=a?e(l,u,d):(a=!0,u),r&&s.next(l)},i&&(()=>{a&&s.next(l),s.complete()})))}}function kt(e,t){return U(t)?pe(e,t,1):pe(e,1)}function un(e){return G((t,n)=>{let r=!1;t.subscribe(B(n,i=>{r=!0,n.next(i)},()=>{r||n.next(e),n.complete()}))})}function Lt(e){return e<=0?()=>Ge:G((t,n)=>{let r=0;t.subscribe(B(n,i=>{++r<=e&&(n.next(i),e<=r&&n.complete())}))})}function ql(e){return P(()=>e)}function Jo(e=rv){return G((t,n)=>{let r=!1;t.subscribe(B(n,i=>{r=!0,n.next(i)},()=>r?n.complete():n.error(e())))})}function rv(){return new Pt}function dn(e){return G((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Dt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Le((i,o)=>e(i,o,r)):ze,Lt(1),n?un(t):Jo(()=>new Pt))}function yr(e){return e<=0?()=>Ge:G((t,n)=>{let r=[];t.subscribe(B(n,i=>{r.push(i),e<r.length&&r.shift()},()=>{for(let i of r)n.next(i);n.complete()},void 0,()=>{r=null}))})}function Zl(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Le((i,o)=>e(i,o,r)):ze,yr(1),n?un(t):Jo(()=>new Pt))}function Kl(e,t){return G(Sh(e,t,arguments.length>=2,!0))}function Ql(...e){let t=ln(e);return G((n,r)=>{(t?mr(e,n,t):mr(e,n)).subscribe(r)})}function Ve(e,t){return G((n,r)=>{let i=null,o=0,s=!1,a=()=>s&&!i&&r.complete();n.subscribe(B(r,l=>{i?.unsubscribe();let c=0,u=o++;he(e(l,u)).subscribe(i=B(r,d=>r.next(t?t(l,d,u,c++):d),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function Yl(e){return G((t,n)=>{he(e).subscribe(B(n,()=>n.complete(),hi)),!n.closed&&t.subscribe(n)})}function Me(e,t,n){let r=U(e)||t||n?{next:e,error:t,complete:n}:e;return r?G((i,o)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;i.subscribe(B(o,l=>{var c;(c=r.next)===null||c===void 0||c.call(r,l),o.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),o.complete()},l=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,l),o.error(l)},()=>{var l,c;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):ze}var pp="https://g.co/ng/security#xss",C=class extends Error{constructor(t,n){super(Ps(t,n)),this.code=t}};function Ps(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Ii(e){return{toString:e}.toString()}var es="__parameters__";function iv(e){return function(...n){if(e){let r=e(...n);for(let i in r)this[i]=r[i]}}}function gp(e,t,n){return Ii(()=>{let r=iv(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;let s=new i(...o);return a.annotation=s,a;function a(l,c,u){let d=l.hasOwnProperty(es)?l[es]:Object.defineProperty(l,es,{value:[]})[es];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var Fe=globalThis;function ne(e){for(let t in e)if(e[t]===ne)return t;throw Error("Could not find renamed property on target object.")}function ov(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Re(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Re).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Ah(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var sv=ne({__forward_ref__:ne});function ks(e){return e.__forward_ref__=ks,e.toString=function(){return Re(this())},e}function Ne(e){return mp(e)?e():e}function mp(e){return typeof e=="function"&&e.hasOwnProperty(sv)&&e.__forward_ref__===ks}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function we(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ls(e){return Th(e,vp)||Th(e,Cp)}function yp(e){return Ls(e)!==null}function Th(e,t){return e.hasOwnProperty(t)?e[t]:null}function av(e){let t=e&&(e[vp]||e[Cp]);return t||null}function xh(e){return e&&(e.hasOwnProperty(Fh)||e.hasOwnProperty(lv))?e[Fh]:null}var vp=ne({\u0275prov:ne}),Fh=ne({\u0275inj:ne}),Cp=ne({ngInjectableDef:ne}),lv=ne({ngInjectorDef:ne}),I=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function wp(e){return e&&!!e.\u0275providers}var cv=ne({\u0275cmp:ne}),uv=ne({\u0275dir:ne}),dv=ne({\u0275pipe:ne}),fv=ne({\u0275mod:ne}),cs=ne({\u0275fac:ne}),mi=ne({__NG_ELEMENT_ID__:ne}),Nh=ne({__NG_ENV_ID__:ne});function br(e){return typeof e=="string"?e:e==null?"":String(e)}function hv(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():br(e)}function pv(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,e)}function Xc(e,t){throw new C(-201,!1)}var V=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(V||{}),fc;function Dp(){return fc}function je(e){let t=fc;return fc=e,t}function Ep(e,t,n){let r=Ls(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&V.Optional)return null;if(t!==void 0)return t;Xc(e,"Injector")}var gv={},vi=gv,hc="__NG_DI_FLAG__",us="ngTempTokenPath",mv="ngTokenPath",yv=/\n/gm,vv="\u0275",Rh="__source",Dr;function Cv(){return Dr}function fn(e){let t=Dr;return Dr=e,t}function wv(e,t=V.Default){if(Dr===void 0)throw new C(-203,!1);return Dr===null?Ep(e,void 0,t):Dr.get(e,t&V.Optional?null:void 0,t)}function _(e,t=V.Default){return(Dp()||wv)(Ne(e),t)}function w(e,t=V.Default){return _(e,Vs(t))}function Vs(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function pc(e){let t=[];for(let n=0;n<e.length;n++){let r=Ne(e[n]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let i,o=V.Default;for(let s=0;s<r.length;s++){let a=r[s],l=Dv(a);typeof l=="number"?l===-1?i=a.token:o|=l:i=a}t.push(_(i,o))}else t.push(_(r))}return t}function bp(e,t){return e[hc]=t,e.prototype[hc]=t,e}function Dv(e){return e[hc]}function Ev(e,t,n,r){let i=e[us];throw t[Rh]&&i.unshift(t[Rh]),e.message=bv(`
`+e.message,i,n,r),e[mv]=i,e[us]=null,e}function bv(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==vv?e.slice(2):e;let i=Re(t);if(Array.isArray(t))i=t.map(Re).join(" -> ");else if(typeof t=="object"){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];o.push(s+":"+(typeof a=="string"?JSON.stringify(a):Re(a)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(yv,`
  `)}`}var js=bp(gp("Optional"),8);var Jc=bp(gp("SkipSelf"),4);function Vn(e,t){let n=e.hasOwnProperty(cs);return n?e[cs]:null}function eu(e,t){e.forEach(n=>Array.isArray(n)?eu(n,t):t(n))}function _p(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ds(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function _v(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(i===1)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;){let o=i-2;e[i]=e[o],i--}e[t]=n,e[t+1]=r}}function Iv(e,t,n){let r=Mi(e,t);return r>=0?e[r|1]=n:(r=~r,_v(e,r,t,n)),r}function Xl(e,t){let n=Mi(e,t);if(n>=0)return e[n|1]}function Mi(e,t){return Mv(e,t,1)}function Mv(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){let o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}var _r={},et=[],Ir=new I(""),Ip=new I("",-1),Mp=new I(""),fs=class{get(t,n=vi){if(n===vi){let r=new Error(`NullInjectorError: No provider for ${Re(t)}!`);throw r.name="NullInjectorError",r}return n}},Sp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Sp||{}),_t=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(_t||{}),gn=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(gn||{});function Sv(e,t,n){let r=e.length;for(;;){let i=e.indexOf(t,n);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function gc(e,t,n){let r=0;for(;r<n.length;){let i=n[r];if(typeof i=="number"){if(i!==0)break;r++;let o=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,o)}else{let o=i,s=n[++r];Av(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function Ap(e){return e===3||e===4||e===6}function Av(e){return e.charCodeAt(0)===64}function Ci(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let i=t[r];typeof i=="number"?n=i:n===0||(n===-1||n===2?Oh(e,n,i,null,t[++r]):Oh(e,n,i,null,null))}}return e}function Oh(e,t,n,r,i){let o=0,s=e.length;if(t===-1)s=-1;else for(;o<e.length;){let a=e[o++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=o-1;break}}}for(;o<e.length;){let a=e[o];if(typeof a=="number")break;if(a===n){if(r===null){i!==null&&(e[o+1]=i);return}else if(r===e[o+1]){e[o+2]=i;return}}o++,r!==null&&o++,i!==null&&o++}s!==-1&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),r!==null&&e.splice(o++,0,r),i!==null&&e.splice(o++,0,i)}var Tp="ng-template";function Tv(e,t,n,r){let i=0;if(r){for(;i<t.length&&typeof t[i]=="string";i+=2)if(t[i]==="class"&&Sv(t[i+1].toLowerCase(),n,0)!==-1)return!0}else if(tu(e))return!1;if(i=t.indexOf(1,i),i>-1){let o;for(;++i<t.length&&typeof(o=t[i])=="string";)if(o.toLowerCase()===n)return!0}return!1}function tu(e){return e.type===4&&e.value!==Tp}function xv(e,t,n){let r=e.type===4&&!n?Tp:e.value;return t===r}function Fv(e,t,n){let r=4,i=e.attrs,o=i!==null?Ov(i):0,s=!1;for(let a=0;a<t.length;a++){let l=t[a];if(typeof l=="number"){if(!s&&!ft(r)&&!ft(l))return!1;if(s&&ft(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!xv(e,l,n)||l===""&&t.length===1){if(ft(r))return!1;s=!0}}else if(r&8){if(i===null||!Tv(e,i,l,n)){if(ft(r))return!1;s=!0}}else{let c=t[++a],u=Nv(l,i,tu(e),n);if(u===-1){if(ft(r))return!1;s=!0;continue}if(c!==""){let d;if(u>o?d="":d=i[u+1].toLowerCase(),r&2&&c!==d){if(ft(r))return!1;s=!0}}}}return ft(r)||s}function ft(e){return(e&1)===0}function Nv(e,t,n,r){if(t===null)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){let s=t[i];if(s===e)return i;if(s===3||s===6)o=!0;else if(s===1||s===2){let a=t[++i];for(;typeof a=="string";)a=t[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=o?1:2}return-1}else return Uv(t,e)}function Rv(e,t,n=!1){for(let r=0;r<t.length;r++)if(Fv(e,t[r],n))return!0;return!1}function Ov(e){for(let t=0;t<e.length;t++){let n=e[t];if(Ap(n))return t}return e.length}function Uv(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Uh(e,t){return e?":not("+t.trim()+")":t}function Pv(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?i+="."+s:r&4&&(i+=" "+s);else i!==""&&!ft(s)&&(t+=Uh(o,i),i=""),r=s,o=o||!ft(r);n++}return i!==""&&(t+=Uh(o,i)),t}function kv(e){return e.map(Pv).join(",")}function Lv(e){let t=[],n=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&t.push(o,e[++r]):i===8&&n.push(o);else{if(!ft(i))break;i=o}r++}return{attrs:t,classes:n}}function ge(e){return Ii(()=>{let t=Up(e),n=Z(E({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Sp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||_t.Emulated,styles:e.styles||et,_:null,schemas:e.schemas||null,tView:null,id:""});Pp(n);let r=e.dependencies;return n.directiveDefs=kh(r,!1),n.pipeDefs=kh(r,!0),n.id=Bv(n),n})}function Vv(e){return mn(e)||Fp(e)}function jv(e){return e!==null}function De(e){return Ii(()=>({type:e.type,bootstrap:e.bootstrap||et,declarations:e.declarations||et,imports:e.imports||et,exports:e.exports||et,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Ph(e,t){if(e==null)return _r;let n={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,s,a=gn.None;Array.isArray(i)?(a=i[0],o=i[1],s=i[2]??o):(o=i,s=i),t?(n[o]=a!==gn.None?[r,a]:r,t[o]=s):n[o]=r}return n}function gt(e){return Ii(()=>{let t=Up(e);return Pp(t),t})}function xp(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function mn(e){return e[cv]||null}function Fp(e){return e[uv]||null}function Np(e){return e[dv]||null}function Rp(e){let t=mn(e)||Fp(e)||Np(e);return t!==null?t.standalone:!1}function Op(e,t){let n=e[fv]||null;if(!n&&t===!0)throw new Error(`Type ${Re(e)} does not have '\u0275mod' property.`);return n}function Up(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||_r,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||et,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Ph(e.inputs,t),outputs:Ph(e.outputs),debugInfo:null}}function Pp(e){e.features?.forEach(t=>t(e))}function kh(e,t){if(!e)return null;let n=t?Np:Vv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(jv)}function Bv(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=**********,"c"+t}function Bs(e){return{\u0275providers:e}}function $v(...e){return{\u0275providers:kp(!0,e),\u0275fromNgModule:!0}}function kp(e,...t){let n=[],r=new Set,i,o=s=>{n.push(s)};return eu(t,s=>{let a=s;mc(a,o,[],r)&&(i||=[],i.push(a))}),i!==void 0&&Lp(i,o),n}function Lp(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:i}=e[n];nu(i,o=>{t(o,r)})}}function mc(e,t,n,r){if(e=Ne(e),!e)return!1;let i=null,o=xh(e),s=!o&&mn(e);if(!o&&!s){let l=e.ngModule;if(o=xh(l),o)i=l;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)mc(c,t,n,r)}}else if(o){if(o.imports!=null&&!a){r.add(i);let c;try{eu(o.imports,u=>{mc(u,t,n,r)&&(c||=[],c.push(u))})}finally{}c!==void 0&&Lp(c,t)}if(!a){let c=Vn(i)||(()=>new i);t({provide:i,useFactory:c,deps:et},i),t({provide:Mp,useValue:i,multi:!0},i),t({provide:Ir,useValue:()=>_(i),multi:!0},i)}let l=o.providers;if(l!=null&&!a){let c=e;nu(l,u=>{t(u,c)})}}else return!1;return i!==e&&e.providers!==void 0}function nu(e,t){for(let n of e)wp(n)&&(n=n.\u0275providers),Array.isArray(n)?nu(n,t):t(n)}var Hv=ne({provide:String,useValue:ne});function Vp(e){return e!==null&&typeof e=="object"&&Hv in e}function zv(e){return!!(e&&e.useExisting)}function Gv(e){return!!(e&&e.useFactory)}function Mr(e){return typeof e=="function"}function Wv(e){return!!e.useClass}var $s=new I(""),os={},qv={},Jl;function ru(){return Jl===void 0&&(Jl=new fs),Jl}var Oe=class{},wi=class extends Oe{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,vc(t,s=>this.processProvider(s)),this.records.set(Ip,vr(void 0,this)),i.has("environment")&&this.records.set(Oe,vr(void 0,this));let o=this.records.get($s);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Mp,et,V.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=K(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),K(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=fn(this),r=je(void 0),i;try{return t()}finally{fn(n),je(r)}}get(t,n=vi,r=V.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Nh))return t[Nh](this);r=Vs(r);let i,o=fn(this),s=je(void 0);try{if(!(r&V.SkipSelf)){let l=this.records.get(t);if(l===void 0){let c=Xv(t)&&Ls(t);c&&this.injectableDefInScope(c)?l=vr(yc(t),os):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let a=r&V.Self?ru():this.parent;return n=r&V.Optional&&n===vi?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[us]=a[us]||[]).unshift(Re(t)),o)throw a;return Ev(a,t,"R3InjectorError",this.source)}else throw a}finally{je(s),fn(o)}}resolveInjectorInitializers(){let t=K(null),n=fn(this),r=je(void 0),i;try{let o=this.get(Ir,et,V.Self);for(let s of o)s()}finally{fn(n),je(r),K(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Re(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){t=Ne(t);let n=Mr(t)?t:Ne(t&&t.provide),r=Kv(t);if(!Mr(t)&&t.multi===!0){let i=this.records.get(n);i||(i=vr(void 0,os,!0),i.factory=()=>pc(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=K(null);try{return n.value===os&&(n.value=qv,n.value=n.factory()),typeof n.value=="object"&&n.value&&Yv(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{K(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=Ne(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function yc(e){let t=Ls(e),n=t!==null?t.factory:Vn(e);if(n!==null)return n;if(e instanceof I)throw new C(204,!1);if(e instanceof Function)return Zv(e);throw new C(204,!1)}function Zv(e){if(e.length>0)throw new C(204,!1);let n=av(e);return n!==null?()=>n.factory(e):()=>new e}function Kv(e){if(Vp(e))return vr(void 0,e.useValue);{let t=jp(e);return vr(t,os)}}function jp(e,t,n){let r;if(Mr(e)){let i=Ne(e);return Vn(i)||yc(i)}else if(Vp(e))r=()=>Ne(e.useValue);else if(Gv(e))r=()=>e.useFactory(...pc(e.deps||[]));else if(zv(e))r=()=>_(Ne(e.useExisting));else{let i=Ne(e&&(e.useClass||e.provide));if(Qv(e))r=()=>new i(...pc(e.deps));else return Vn(i)||yc(i)}return r}function vr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Qv(e){return!!e.deps}function Yv(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Xv(e){return typeof e=="function"||typeof e=="object"&&e instanceof I}function vc(e,t){for(let n of e)Array.isArray(n)?vc(n,t):n&&wp(n)?vc(n.\u0275providers,t):t(n)}function qe(e,t){e instanceof wi&&e.assertNotDestroyed();let n,r=fn(e),i=je(void 0);try{return t()}finally{fn(r),je(i)}}function Bp(){return Dp()!==void 0||Cv()!=null}function Jv(e){if(!Bp())throw new C(-203,!1)}function e2(e){let t=Fe.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function t2(e){return typeof e=="function"}var Bt=0,j=1,R=2,Ue=3,ht=4,mt=5,hs=6,ps=7,pt=8,Sr=9,It=10,Te=11,Di=12,Lh=13,Pr=14,Mt=15,Ar=16,Cr=17,Tr=18,Hs=19,$p=20,hn=21,ec=22,tt=23,St=25,Hp=1;var jn=7,gs=8,ms=9,nt=10,ys=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(ys||{});function pn(e){return Array.isArray(e)&&typeof e[Hp]=="object"}function $t(e){return Array.isArray(e)&&e[Hp]===!0}function zp(e){return(e.flags&4)!==0}function zs(e){return e.componentOffset>-1}function iu(e){return(e.flags&1)===1}function yn(e){return!!e.template}function Cc(e){return(e[R]&512)!==0}var wc=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Gp(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function kr(){return Wp}function Wp(e){return e.type.prototype.ngOnChanges&&(e.setInput=r2),n2}kr.ngInherit=!0;function n2(){let e=Zp(this),t=e?.current;if(t){let n=e.previous;if(n===_r)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function r2(e,t,n,r,i){let o=this.declaredInputs[r],s=Zp(e)||i2(e,{previous:_r,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[o];a[o]=new wc(c&&c.currentValue,n,l===_r),Gp(e,t,i,n)}var qp="__ngSimpleChanges__";function Zp(e){return e[qp]||null}function i2(e,t){return e[qp]=t}var Vh=null;var Et=function(e,t,n){Vh?.(e,t,n)},Kp="svg",o2="math";function At(e){for(;Array.isArray(e);)e=e[Bt];return e}function Qp(e,t){return At(t[e])}function rt(e,t){return At(t[e.index])}function Yp(e,t){return e.data[t]}function s2(e,t){return e[t]}function wn(e,t){let n=t[e];return pn(n)?n:n[Bt]}function ou(e){return(e[R]&128)===128}function a2(e){return $t(e[Ue])}function vs(e,t){return t==null?null:e[t]}function Xp(e){e[Cr]=0}function Jp(e){e[R]&1024||(e[R]|=1024,ou(e)&&Gs(e))}function l2(e,t){for(;e>0;)t=t[Pr],e--;return t}function Ei(e){return!!(e[R]&9216||e[tt]?.dirty)}function Dc(e){e[It].changeDetectionScheduler?.notify(7),e[R]&64&&(e[R]|=1024),Ei(e)&&Gs(e)}function Gs(e){e[It].changeDetectionScheduler?.notify(0);let t=Bn(e);for(;t!==null&&!(t[R]&8192||(t[R]|=8192,!ou(t)));)t=Bn(t)}function eg(e,t){if((e[R]&256)===256)throw new C(911,!1);e[hn]===null&&(e[hn]=[]),e[hn].push(t)}function c2(e,t){if(e[hn]===null)return;let n=e[hn].indexOf(t);n!==-1&&e[hn].splice(n,1)}function Bn(e){let t=e[Ue];return $t(t)?t[Ue]:t}var L={lFrame:dg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var tg=!1;function u2(){return L.lFrame.elementDepthCount}function d2(){L.lFrame.elementDepthCount++}function f2(){L.lFrame.elementDepthCount--}function ng(){return L.bindingsEnabled}function h2(){return L.skipHydrationRootTNode!==null}function p2(e){return L.skipHydrationRootTNode===e}function g2(){L.skipHydrationRootTNode=null}function re(){return L.lFrame.lView}function it(){return L.lFrame.tView}function su(e){return L.lFrame.contextLView=e,e[pt]}function au(e){return L.lFrame.contextLView=null,e}function $e(){let e=rg();for(;e!==null&&e.type===64;)e=e.parent;return e}function rg(){return L.lFrame.currentTNode}function m2(){let e=L.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Si(e,t){let n=L.lFrame;n.currentTNode=e,n.isParent=t}function ig(){return L.lFrame.isParent}function y2(){L.lFrame.isParent=!1}function og(){return tg}function jh(e){tg=e}function sg(){let e=L.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function v2(){return L.lFrame.bindingIndex}function C2(e){return L.lFrame.bindingIndex=e}function lu(){return L.lFrame.bindingIndex++}function ag(e){let t=L.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function w2(){return L.lFrame.inI18n}function D2(e,t){let n=L.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ec(t)}function E2(){return L.lFrame.currentDirectiveIndex}function Ec(e){L.lFrame.currentDirectiveIndex=e}function b2(e){let t=L.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function lg(e){L.lFrame.currentQueryIndex=e}function _2(e){let t=e[j];return t.type===2?t.declTNode:t.type===1?e[mt]:null}function cg(e,t,n){if(n&V.SkipSelf){let i=t,o=e;for(;i=i.parent,i===null&&!(n&V.Host);)if(i=_2(o),i===null||(o=o[Pr],i.type&10))break;if(i===null)return!1;t=i,e=o}let r=L.lFrame=ug();return r.currentTNode=t,r.lView=e,!0}function cu(e){let t=ug(),n=e[j];L.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ug(){let e=L.lFrame,t=e===null?null:e.child;return t===null?dg(e):t}function dg(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function fg(){let e=L.lFrame;return L.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var hg=fg;function uu(){let e=fg();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function I2(e){return(L.lFrame.contextLView=l2(e,L.lFrame.contextLView))[pt]}function qn(){return L.lFrame.selectedIndex}function $n(e){L.lFrame.selectedIndex=e}function pg(){let e=L.lFrame;return Yp(e.tView,e.selectedIndex)}function Lr(){L.lFrame.currentNamespace=Kp}function Vr(){M2()}function M2(){L.lFrame.currentNamespace=null}function S2(){return L.lFrame.currentNamespace}var gg=!0;function du(){return gg}function fu(e){gg=e}function A2(e,t,n){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){let s=Wp(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}function hu(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=o;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ss(e,t,n){mg(e,t,3,n)}function as(e,t,n,r){(e[R]&3)===n&&mg(e,t,n,r)}function tc(e,t){let n=e[R];(n&3)===t&&(n&=16383,n+=1,e[R]=n)}function mg(e,t,n,r){let i=r!==void 0?e[Cr]&65535:0,o=r??-1,s=t.length-1,a=0;for(let l=i;l<s;l++)if(typeof t[l+1]=="number"){if(a=t[l],r!=null&&a>=r)break}else t[l]<0&&(e[Cr]+=65536),(a<o||o==-1)&&(T2(e,n,t,l),e[Cr]=(e[Cr]&**********)+l+2),l++}function Bh(e,t){Et(4,e,t);let n=K(null);try{t.call(e)}finally{K(n),Et(5,e,t)}}function T2(e,t,n,r){let i=n[r]<0,o=n[r+1],s=i?-n[r]:n[r],a=e[s];i?e[R]>>14<e[Cr]>>16&&(e[R]&3)===t&&(e[R]+=16384,Bh(a,o)):Bh(a,o)}var Er=-1,Hn=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function x2(e){return e instanceof Hn}function F2(e){return(e.flags&8)!==0}function N2(e){return(e.flags&16)!==0}var nc={},bc=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Vs(r);let i=this.injector.get(t,nc,r);return i!==nc||n===nc?i:this.parentInjector.get(t,n,r)}};function yg(e){return e!==Er}function Cs(e){return e&32767}function R2(e){return e>>16}function ws(e,t){let n=R2(e),r=t;for(;n>0;)r=r[Pr],n--;return r}var _c=!0;function Ds(e){let t=_c;return _c=e,t}var O2=256,vg=O2-1,Cg=5,U2=0,bt={};function P2(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(mi)&&(r=n[mi]),r==null&&(r=n[mi]=U2++);let i=r&vg,o=1<<i;t.data[e+(i>>Cg)]|=o}function Es(e,t){let n=wg(e,t);if(n!==-1)return n;let r=t[j];r.firstCreatePass&&(e.injectorIndex=t.length,rc(r.data,e),rc(t,null),rc(r.blueprint,null));let i=pu(e,t),o=e.injectorIndex;if(yg(i)){let s=Cs(i),a=ws(i,t),l=a[j].data;for(let c=0;c<8;c++)t[o+c]=a[s+c]|l[s+c]}return t[o+8]=i,o}function rc(e,t){e.push(0,0,0,0,0,0,0,0,t)}function wg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function pu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;i!==null;){if(r=Ig(i),r===null)return Er;if(n++,i=i[Pr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Er}function Ic(e,t,n){P2(e,t,n)}function k2(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,i=0;for(;i<r;){let o=n[i];if(Ap(o))break;if(o===0)i=i+2;else if(typeof o=="number")for(i++;i<r&&typeof n[i]=="string";)i++;else{if(o===t)return n[i+1];i=i+2}}}return null}function Dg(e,t,n){if(n&V.Optional||e!==void 0)return e;Xc(t,"NodeInjector")}function Eg(e,t,n,r){if(n&V.Optional&&r===void 0&&(r=null),!(n&(V.Self|V.Host))){let i=e[Sr],o=je(void 0);try{return i?i.get(t,r,n&V.Optional):Ep(t,r,n&V.Optional)}finally{je(o)}}return Dg(r,t,n)}function bg(e,t,n,r=V.Default,i){if(e!==null){if(t[R]&2048&&!(r&V.Self)){let s=$2(e,t,n,r,bt);if(s!==bt)return s}let o=_g(e,t,n,r,bt);if(o!==bt)return o}return Eg(t,n,r,i)}function _g(e,t,n,r,i){let o=j2(n);if(typeof o=="function"){if(!cg(t,e,r))return r&V.Host?Dg(i,n,r):Eg(t,n,r,i);try{let s;if(s=o(r),s==null&&!(r&V.Optional))Xc(n);else return s}finally{hg()}}else if(typeof o=="number"){let s=null,a=wg(e,t),l=Er,c=r&V.Host?t[Mt][mt]:null;for((a===-1||r&V.SkipSelf)&&(l=a===-1?pu(e,t):t[a+8],l===Er||!Hh(r,!1)?a=-1:(s=t[j],a=Cs(l),t=ws(l,t)));a!==-1;){let u=t[j];if($h(o,a,u.data)){let d=L2(a,t,n,s,r,c);if(d!==bt)return d}l=t[a+8],l!==Er&&Hh(r,t[j].data[a+8]===c)&&$h(o,a,t)?(s=u,a=Cs(l),t=ws(l,t)):a=-1}}return i}function L2(e,t,n,r,i,o){let s=t[j],a=s.data[e+8],l=r==null?zs(a)&&_c:r!=s&&(a.type&3)!==0,c=i&V.Host&&o===a,u=V2(a,s,n,l,c);return u!==null?xr(t,s,u,a):bt}function V2(e,t,n,r,i){let o=e.providerIndexes,s=t.data,a=o&1048575,l=e.directiveStart,c=e.directiveEnd,u=o>>20,d=r?a:a+u,g=i?a+u:c;for(let f=d;f<g;f++){let m=s[f];if(f<l&&n===m||f>=l&&m.type===n)return f}if(i){let f=s[l];if(f&&yn(f)&&f.type===n)return l}return null}function xr(e,t,n,r){let i=e[n],o=t.data;if(x2(i)){let s=i;s.resolving&&pv(hv(o[n]));let a=Ds(s.canSeeViewProviders);s.resolving=!0;let l,c=s.injectImpl?je(s.injectImpl):null,u=cg(e,r,V.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&A2(n,o[n],t)}finally{c!==null&&je(c),Ds(a),s.resolving=!1,hg()}}return i}function j2(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(mi)?e[mi]:void 0;return typeof t=="number"?t>=0?t&vg:B2:t}function $h(e,t,n){let r=1<<e;return!!(n[t+(e>>Cg)]&r)}function Hh(e,t){return!(e&V.Self)&&!(e&V.Host&&t)}var Ln=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return bg(this._tNode,this._lView,t,Vs(r),n)}};function B2(){return new Ln($e(),re())}function Ws(e){return Ii(()=>{let t=e.prototype.constructor,n=t[cs]||Mc(t),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[cs]||Mc(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function Mc(e){return mp(e)?()=>{let t=Mc(Ne(e));return t&&t()}:Vn(e)}function $2(e,t,n,r,i){let o=e,s=t;for(;o!==null&&s!==null&&s[R]&2048&&!(s[R]&512);){let a=_g(o,s,n,r|V.Self,bt);if(a!==bt)return a;let l=o.parent;if(!l){let c=s[$p];if(c){let u=c.get(n,bt,r);if(u!==bt)return u}l=Ig(s),s=s[Pr]}o=l}return i}function Ig(e){let t=e[j],n=t.type;return n===2?t.declTNode:n===1?e[mt]:null}function gu(e){return k2($e(),e)}function zh(e,t=null,n=null,r){let i=Mg(e,t,n,r);return i.resolveInjectorInitializers(),i}function Mg(e,t=null,n=null,r,i=new Set){let o=[n||et,$v(e)];return r=r||(typeof e=="object"?void 0:Re(e)),new wi(o,t||ru(),r||null,i)}var We=class e{static{this.THROW_IF_NOT_FOUND=vi}static{this.NULL=new fs}static create(t,n){if(Array.isArray(t))return zh({name:""},n,t,"");{let r=t.name??"";return zh({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=b({token:e,providedIn:"any",factory:()=>_(Ip)})}static{this.__NG_ELEMENT_ID__=-1}};var H2=new I("");H2.__NG_ELEMENT_ID__=e=>{let t=$e();if(t===null)throw new C(204,!1);if(t.type&2)return t.value;if(e&V.Optional)return null;throw new C(204,!1)};var z2="ngOriginalError";function ic(e){return e[z2]}var mu=(()=>{class e{static{this.__NG_ELEMENT_ID__=G2}static{this.__NG_ENV_ID__=n=>n}}return e})(),Sc=class extends mu{constructor(t){super(),this._lView=t}onDestroy(t){return eg(this._lView,t),()=>c2(this._lView,t)}};function G2(){return new Sc(re())}var Ht=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Ae(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Ac=class extends Ie{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,Bp()&&(this.destroyRef=w(mu,{optional:!0})??void 0,this.pendingTasks=w(Ht,{optional:!0})??void 0)}emit(t){let n=K(null);try{super.next(t)}finally{K(n)}}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&typeof t=="object"){let l=t;i=l.next?.bind(l),o=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:o,complete:s});return t instanceof fe&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},Ce=Ac;function bs(...e){}function Sg(e){let t,n;function r(){e=bs;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Gh(e){return queueMicrotask(()=>e()),()=>{e=bs}}var X=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ce(!1),this.onMicrotaskEmpty=new Ce(!1),this.onStable=new Ce(!1),this.onError=new Ce(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.callbackScheduled=!1,Z2(i)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){let o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,W2,bs,bs);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},W2={};function yu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function q2(e){e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,Zone.root.run(()=>{Sg(()=>{e.callbackScheduled=!1,Tc(e),e.isCheckStableRunning=!0,yu(e),e.isCheckStableRunning=!1})}),Tc(e))}function Z2(e){let t=()=>{q2(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,o,s,a)=>{if(K2(a))return n.invokeTask(i,o,s,a);try{return Wh(e),n.invokeTask(i,o,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&o.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),qh(e)}},onInvoke:(n,r,i,o,s,a,l)=>{try{return Wh(e),n.invoke(i,o,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Q2(a)&&t(),qh(e)}},onHasTask:(n,r,i,o)=>{n.hasTask(i,o),r===i&&(o.change=="microTask"?(e._hasPendingMicrotasks=o.microTask,Tc(e),yu(e)):o.change=="macroTask"&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,r,i,o)=>(n.handleError(i,o),e.runOutsideAngular(()=>e.onError.emit(o)),!1)})}function Tc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Wh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function qh(e){e._nesting--,yu(e)}var _s=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Ce,this.onMicrotaskEmpty=new Ce,this.onStable=new Ce,this.onError=new Ce}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}};function K2(e){return Ag(e,"__ignore_ng_zone__")}function Q2(e){return Ag(e,"__scheduler_tick__")}function Ag(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function Y2(e="zone.js",t){return e==="noop"?new _s:e==="zone.js"?new X(t):e}var Vt=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&ic(t);for(;n&&ic(n);)n=ic(n);return n||null}},X2=new I("",{providedIn:"root",factory:()=>{let e=w(X),t=w(Vt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function J2(){return qs($e(),re())}function qs(e,t){return new zt(rt(e,t))}var zt=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=J2}}return e})();function Tg(e){return(e.flags&128)===128}var xg=new Map,eC=0;function tC(){return eC++}function nC(e){xg.set(e[Hs],e)}function rC(e){xg.delete(e[Hs])}var Zh="__ngContext__";function zn(e,t){pn(t)?(e[Zh]=t[Hs],nC(t)):e[Zh]=t}function Fg(e){return Rg(e[Di])}function Ng(e){return Rg(e[ht])}function Rg(e){for(;e!==null&&!$t(e);)e=e[ht];return e}var xc;function Og(e){xc=e}function iC(){if(xc!==void 0)return xc;if(typeof document<"u")return document;throw new C(210,!1)}var Zs=new I("",{providedIn:"root",factory:()=>oC}),oC="ng",vu=new I(""),Tt=new I("",{providedIn:"platform",factory:()=>"unknown"});var Cu=new I(""),wu=new I("",{providedIn:"root",factory:()=>iC().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var sC="h",aC="b";var lC=()=>null;function Du(e,t,n=!1){return lC(e,t,n)}var Ug=!1,cC=new I("",{providedIn:"root",factory:()=>Ug});var ts;function uC(){if(ts===void 0&&(ts=null,Fe.trustedTypes))try{ts=Fe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ts}function Kh(e){return uC()?.createScriptURL(e)||e}var Is=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${pp})`}};function Ai(e){return e instanceof Is?e.changingThisBreaksApplicationSecurity:e}function Eu(e,t){let n=dC(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${pp})`)}return n===t}function dC(e){return e instanceof Is&&e.getTypeName()||null}var fC=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Pg(e){return e=String(e),e.match(fC)?e:"unsafe:"+e}var Ks=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ks||{});function Zn(e){let t=Lg();return t?t.sanitize(Ks.URL,e)||"":Eu(e,"URL")?Ai(e):Pg(br(e))}function hC(e){let t=Lg();if(t)return Kh(t.sanitize(Ks.RESOURCE_URL,e)||"");if(Eu(e,"ResourceURL"))return Kh(Ai(e));throw new C(904,!1)}function pC(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?hC:Zn}function kg(e,t,n){return pC(t,n)(e)}function Lg(){let e=re();return e&&e[It].sanitizer}function Vg(e){return e instanceof Function?e():e}function gC(e){return(e??w(We)).get(Tt)==="browser"}var jt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(jt||{}),mC;function bu(e,t){return mC(e,t)}function wr(e,t,n,r,i){if(r!=null){let o,s=!1;$t(r)?o=r:pn(r)&&(s=!0,r=r[Bt]);let a=At(r);e===0&&n!==null?i==null?zg(t,n,a):Ms(t,n,a,i||null,!0):e===1&&n!==null?Ms(t,n,a,i||null,!0):e===2?FC(t,a,s):e===3&&t.destroyNode(a),o!=null&&RC(t,e,o,n,i)}}function yC(e,t){return e.createText(t)}function vC(e,t,n){e.setValue(t,n)}function jg(e,t,n){return e.createElement(t,n)}function CC(e,t){Bg(e,t),t[Bt]=null,t[mt]=null}function wC(e,t,n,r,i,o){r[Bt]=i,r[mt]=t,Qs(e,r,n,1,i,o)}function Bg(e,t){t[It].changeDetectionScheduler?.notify(8),Qs(e,t,t[Te],2,null,null)}function DC(e){let t=e[Di];if(!t)return oc(e[j],e);for(;t;){let n=null;if(pn(t))n=t[Di];else{let r=t[nt];r&&(n=r)}if(!n){for(;t&&!t[ht]&&t!==e;)pn(t)&&oc(t[j],t),t=t[Ue];t===null&&(t=e),pn(t)&&oc(t[j],t),n=t&&t[ht]}t=n}}function EC(e,t,n,r){let i=nt+r,o=n.length;r>0&&(n[i-1][ht]=t),r<o-nt?(t[ht]=n[i],_p(n,nt+r,t)):(n.push(t),t[ht]=null),t[Ue]=n;let s=t[Ar];s!==null&&n!==s&&$g(s,t);let a=t[Tr];a!==null&&a.insertView(e),Dc(t),t[R]|=128}function $g(e,t){let n=e[ms],r=t[Ue];if(pn(r))e[R]|=ys.HasTransplantedViews;else{let i=r[Ue][Mt];t[Mt]!==i&&(e[R]|=ys.HasTransplantedViews)}n===null?e[ms]=[t]:n.push(t)}function _u(e,t){let n=e[ms],r=n.indexOf(t);n.splice(r,1)}function Fc(e,t){if(e.length<=nt)return;let n=nt+t,r=e[n];if(r){let i=r[Ar];i!==null&&i!==e&&_u(i,r),t>0&&(e[n-1][ht]=r[ht]);let o=ds(e,nt+t);CC(r[j],r);let s=o[Tr];s!==null&&s.detachView(o[j]),r[Ue]=null,r[ht]=null,r[R]&=-129}return r}function Hg(e,t){if(!(t[R]&256)){let n=t[Te];n.destroyNode&&Qs(e,t,n,3,null,null),DC(t)}}function oc(e,t){if(t[R]&256)return;let n=K(null);try{t[R]&=-129,t[R]|=256,t[tt]&&Ol(t[tt]),_C(e,t),bC(e,t),t[j].type===1&&t[Te].destroy();let r=t[Ar];if(r!==null&&$t(t[Ue])){r!==t[Ue]&&_u(r,t);let i=t[Tr];i!==null&&i.detachView(e)}rC(t)}finally{K(n)}}function bC(e,t){let n=e.cleanup,r=t[ps];if(n!==null)for(let o=0;o<n.length-1;o+=2)if(typeof n[o]=="string"){let s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else{let s=r[n[o+1]];n[o].call(s)}r!==null&&(t[ps]=null);let i=t[hn];if(i!==null){t[hn]=null;for(let o=0;o<i.length;o++){let s=i[o];s()}}}function _C(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let i=t[n[r]];if(!(i instanceof Hn)){let o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){let a=i[o[s]],l=o[s+1];Et(4,a,l);try{l.call(a)}finally{Et(5,a,l)}}else{Et(4,i,o);try{o.call(i)}finally{Et(5,i,o)}}}}}function IC(e,t,n){return MC(e,t.parent,n)}function MC(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Bt];{let{componentOffset:i}=r;if(i>-1){let{encapsulation:o}=e.data[r.directiveStart+i];if(o===_t.None||o===_t.Emulated)return null}return rt(r,n)}}function Ms(e,t,n,r,i){e.insertBefore(t,n,r,i)}function zg(e,t,n){e.appendChild(t,n)}function Qh(e,t,n,r,i){r!==null?Ms(e,t,n,r,i):zg(e,t,n)}function Gg(e,t){return e.parentNode(t)}function SC(e,t){return e.nextSibling(t)}function AC(e,t,n){return xC(e,t,n)}function TC(e,t,n){return e.type&40?rt(e,n):null}var xC=TC,Yh;function Iu(e,t,n,r){let i=IC(e,r,t),o=t[Te],s=r.parent||t[mt],a=AC(s,r,t);if(i!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)Qh(o,i,n[l],a,!1);else Qh(o,i,n,a,!1);Yh!==void 0&&Yh(o,r,t,n,i)}function gi(e,t){if(t!==null){let n=t.type;if(n&3)return rt(t,e);if(n&4)return Nc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return gi(e,r);{let i=e[t.index];return $t(i)?Nc(-1,i):At(i)}}else{if(n&128)return gi(e,t.next);if(n&32)return bu(t,e)()||At(e[t.index]);{let r=Wg(e,t);if(r!==null){if(Array.isArray(r))return r[0];let i=Bn(e[Mt]);return gi(i,r)}else return gi(e,t.next)}}}return null}function Wg(e,t){if(t!==null){let r=e[Mt][mt],i=t.projection;return r.projection[i]}return null}function Nc(e,t){let n=nt+e+1;if(n<t.length){let r=t[n],i=r[j].firstChild;if(i!==null)return gi(r,i)}return t[jn]}function FC(e,t,n){e.removeChild(null,t,n)}function Mu(e,t,n,r,i,o,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&t===0&&(a&&zn(At(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)Mu(e,t,n.child,r,i,o,!1),wr(t,e,i,a,o);else if(l&32){let c=bu(n,r),u;for(;u=c();)wr(t,e,i,u,o);wr(t,e,i,a,o)}else l&16?NC(e,t,r,n,i,o):wr(t,e,i,a,o);n=s?n.projectionNext:n.next}}function Qs(e,t,n,r,i,o){Mu(n,r,e.firstChild,t,i,o,!1)}function NC(e,t,n,r,i,o){let s=n[Mt],l=s[mt].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];wr(t,e,i,u,o)}else{let c=l,u=s[Ue];Tg(r)&&(c.flags|=128),Mu(e,t,c,u,i,o,!0)}}function RC(e,t,n,r,i){let o=n[jn],s=At(n);o!==s&&wr(t,e,r,o,i);for(let a=nt;a<n.length;a++){let l=n[a];Qs(l[j],l,e,t,r,o)}}function OC(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=r.indexOf("-")===-1?void 0:jt.DashCase;i==null?e.removeStyle(n,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=jt.Important),e.setStyle(n,r,i,o))}}function UC(e,t,n){e.setAttribute(t,"style",n)}function qg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Zg(e,t,n){let{mergedAttrs:r,classes:i,styles:o}=n;r!==null&&gc(e,t,r),i!==null&&qg(e,t,i),o!==null&&UC(e,t,o)}var Gt={};function J(e=1){Kg(it(),re(),qn()+e,!1)}function Kg(e,t,n,r){if(!r)if((t[R]&3)===3){let o=e.preOrderCheckHooks;o!==null&&ss(t,o,n)}else{let o=e.preOrderHooks;o!==null&&as(t,o,0,n)}$n(n)}function $(e,t=V.Default){let n=re();if(n===null)return _(e,t);let r=$e();return bg(r,n,Ne(e),t)}function Qg(){let e="invalid";throw new Error(e)}function Yg(e,t,n,r,i,o){let s=K(null);try{let a=null;i&gn.SignalBased&&(a=t[r][an]),a!==null&&a.transformFn!==void 0&&(o=a.transformFn(o)),i&gn.HasDecoratorInputTransform&&(o=e.inputTransforms[r].call(t,o)),e.setInput!==null?e.setInput(t,a,o,n,r):Gp(t,a,r,o)}finally{K(s)}}function PC(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let i=n[r];if(i<0)$n(~i);else{let o=i,s=n[++r],a=n[++r];D2(s,o);let l=t[o];a(2,l)}}}finally{$n(-1)}}function Ys(e,t,n,r,i,o,s,a,l,c,u){let d=t.blueprint.slice();return d[Bt]=i,d[R]=r|4|128|8|64,(c!==null||e&&e[R]&2048)&&(d[R]|=2048),Xp(d),d[Ue]=d[Pr]=e,d[pt]=n,d[It]=s||e&&e[It],d[Te]=a||e&&e[Te],d[Sr]=l||e&&e[Sr]||null,d[mt]=o,d[Hs]=tC(),d[hs]=u,d[$p]=c,d[Mt]=t.type==2?e[Mt]:d,d}function Xs(e,t,n,r,i){let o=e.data[t];if(o===null)o=kC(e,t,n,r,i),w2()&&(o.flags|=32);else if(o.type&64){o.type=n,o.value=r,o.attrs=i;let s=m2();o.injectorIndex=s===null?-1:s.injectorIndex}return Si(o,!0),o}function kC(e,t,n,r,i){let o=rg(),s=ig(),a=s?o:o&&o.parent,l=e.data[t]=$C(e,a,n,t,r,i);return e.firstChild===null&&(e.firstChild=l),o!==null&&(s?o.child==null&&l.parent!==null&&(o.child=l):o.next===null&&(o.next=l,l.prev=o)),l}function Xg(e,t,n,r){if(n===0)return-1;let i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function Jg(e,t,n,r,i){let o=qn(),s=r&2;try{$n(-1),s&&t.length>St&&Kg(e,t,St,!1),Et(s?2:0,i),n(r,i)}finally{$n(o),Et(s?3:1,i)}}function em(e,t,n){if(zp(t)){let r=K(null);try{let i=t.directiveStart,o=t.directiveEnd;for(let s=i;s<o;s++){let a=e.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{K(r)}}}function tm(e,t,n){ng()&&(KC(e,t,n,rt(n,t)),(n.flags&64)===64&&sm(e,t,n))}function nm(e,t,n=rt){let r=t.localNames;if(r!==null){let i=t.index+1;for(let o=0;o<r.length;o+=2){let s=r[o+1],a=s===-1?n(t,e):e[s];e[i++]=a}}}function rm(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Su(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Su(e,t,n,r,i,o,s,a,l,c,u){let d=St+r,g=d+i,f=LC(d,g),m=typeof c=="function"?c():c;return f[j]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:u}}function LC(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Gt);return n}function VC(e,t,n,r){let o=r.get(cC,Ug)||n===_t.ShadowDom,s=e.selectRootElement(t,o);return jC(s),s}function jC(e){BC(e)}var BC=()=>null;function $C(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,a=0;return h2()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Xh(e,t,n,r,i){for(let o in t){if(!t.hasOwnProperty(o))continue;let s=t[o];if(s===void 0)continue;r??={};let a,l=gn.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let c=o;if(i!==null){if(!i.hasOwnProperty(o))continue;c=i[o]}e===0?Jh(r,n,c,a,l):Jh(r,n,c,a)}return r}function Jh(e,t,n,r,i){let o;e.hasOwnProperty(n)?(o=e[n]).push(t,r):o=e[n]=[t,r],i!==void 0&&o.push(i)}function HC(e,t,n){let r=t.directiveStart,i=t.directiveEnd,o=e.data,s=t.attrs,a=[],l=null,c=null;for(let u=r;u<i;u++){let d=o[u],g=n?n.get(d):null,f=g?g.inputs:null,m=g?g.outputs:null;l=Xh(0,d.inputs,u,l,f),c=Xh(1,d.outputs,u,c,m);let y=l!==null&&s!==null&&!tu(t)?sw(l,u,s):null;a.push(y)}l!==null&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}function zC(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function GC(e,t,n,r,i,o,s,a){let l=rt(t,n),c=t.inputs,u;!a&&c!=null&&(u=c[r])?(Au(e,n,u,r,i),zs(t)&&WC(n,t.index)):t.type&3?(r=zC(r),i=s!=null?s(i,t.value||"",r):i,o.setProperty(l,r,i)):t.type&12}function WC(e,t){let n=wn(t,e);n[R]&16||(n[R]|=64)}function im(e,t,n,r){if(ng()){let i=r===null?null:{"":-1},o=YC(e,n),s,a;o===null?s=a=null:[s,a]=o,s!==null&&om(e,t,n,s,i,a),i&&XC(n,r,i)}n.mergedAttrs=Ci(n.mergedAttrs,n.attrs)}function om(e,t,n,r,i,o){for(let c=0;c<r.length;c++)Ic(Es(n,t),e,r[c].type);ew(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=Xg(e,t,r.length,null);for(let c=0;c<r.length;c++){let u=r[c];n.mergedAttrs=Ci(n.mergedAttrs,u.hostAttrs),tw(e,n,t,l,u),JC(l,u,i),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}HC(e,n,o)}function qC(e,t,n,r,i){let o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ZC(s)!=a&&s.push(a),s.push(n,r,o)}}function ZC(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function KC(e,t,n,r){let i=n.directiveStart,o=n.directiveEnd;zs(n)&&nw(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||Es(n,t),zn(r,t);let s=n.initialInputs;for(let a=i;a<o;a++){let l=e.data[a],c=xr(t,e,a,n);if(zn(c,t),s!==null&&ow(t,a-i,c,l,n,s),yn(l)){let u=wn(n.index,t);u[pt]=xr(t,e,a,n)}}}function sm(e,t,n){let r=n.directiveStart,i=n.directiveEnd,o=n.index,s=E2();try{$n(o);for(let a=r;a<i;a++){let l=e.data[a],c=t[a];Ec(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&QC(l,c)}}finally{$n(-1),Ec(s)}}function QC(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function YC(e,t){let n=e.directiveRegistry,r=null,i=null;if(n)for(let o=0;o<n.length;o++){let s=n[o];if(Rv(t,s.selectors,!1))if(r||(r=[]),yn(s))if(s.findHostDirectiveDefs!==null){let a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s);let l=a.length;Rc(e,t,l)}else r.unshift(s),Rc(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return r===null?null:[r,i]}function Rc(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function XC(e,t,n){if(t){let r=e.localNames=[];for(let i=0;i<t.length;i+=2){let o=n[t[i+1]];if(o==null)throw new C(-301,!1);r.push(t[i],o)}}}function JC(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;yn(t)&&(n[""]=e)}}function ew(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function tw(e,t,n,r,i){e.data[r]=i;let o=i.factory||(i.factory=Vn(i.type,!0)),s=new Hn(o,yn(i),$);e.blueprint[r]=s,n[r]=s,qC(e,t,r,Xg(e,n,i.hostVars,Gt),i)}function nw(e,t,n){let r=rt(t,e),i=rm(n),o=e[It].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=Js(e,Ys(e,i,null,s,r,t,null,o.createRenderer(r,n),null,null,null));e[t.index]=a}function rw(e,t,n,r,i,o){let s=rt(e,t);iw(t[Te],s,o,e.value,n,r,i)}function iw(e,t,n,r,i,o,s){if(o==null)e.removeAttribute(t,i,n);else{let a=s==null?br(o):s(o,r||"",i);e.setAttribute(t,i,a,n)}}function ow(e,t,n,r,i,o){let s=o[t];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],c=s[a++],u=s[a++],d=s[a++];Yg(r,n,l,c,u,d)}}function sw(e,t,n){let r=null,i=0;for(;i<n.length;){let o=n[i];if(o===0){i+=4;continue}else if(o===5){i+=2;continue}if(typeof o=="number")break;if(e.hasOwnProperty(o)){r===null&&(r=[]);let s=e[o];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(o,s[a+1],s[a+2],n[i+1]);break}}i+=2}return r}function am(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function lm(e,t){let n=e.contentQueries;if(n!==null){let r=K(null);try{for(let i=0;i<n.length;i+=2){let o=n[i],s=n[i+1];if(s!==-1){let a=e.data[s];lg(o),a.contentQueries(2,t[s],s)}}}finally{K(r)}}}function Js(e,t){return e[Di]?e[Lh][ht]=t:e[Di]=t,e[Lh]=t,t}function Oc(e,t,n){lg(0);let r=K(null);try{t(e,n)}finally{K(r)}}function aw(e){return e[ps]??=[]}function lw(e){return e.cleanup??=[]}function cm(e,t){let n=e[Sr],r=n?n.get(Vt,null):null;r&&r.handleError(t)}function Au(e,t,n,r,i){for(let o=0;o<n.length;){let s=n[o++],a=n[o++],l=n[o++],c=t[s],u=e.data[s];Yg(u,c,r,a,l,i)}}function um(e,t,n){let r=Qp(t,e);vC(e[Te],r,n)}function cw(e,t){let n=wn(t,e),r=n[j];uw(r,n);let i=n[Bt];i!==null&&n[hs]===null&&(n[hs]=Du(i,n[Sr])),Tu(r,n,n[pt])}function uw(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Tu(e,t,n){cu(t);try{let r=e.viewQuery;r!==null&&Oc(1,r,n);let i=e.template;i!==null&&Jg(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Tr]?.finishViewCreation(e),e.staticContentQueries&&lm(e,t),e.staticViewQueries&&Oc(2,e.viewQuery,n);let o=e.components;o!==null&&dw(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[R]&=-5,uu()}}function dw(e,t){for(let n=0;n<t.length;n++)cw(e,t[n])}function fw(e,t,n,r){let i=K(null);try{let o=t.tView,a=e[R]&4096?4096:16,l=Ys(e,o,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];l[Ar]=c;let u=e[Tr];return u!==null&&(l[Tr]=u.createEmbeddedView(o)),Tu(o,l,n),l}finally{K(i)}}function ep(e,t){return!t||t.firstChild===null||Tg(e)}function hw(e,t,n,r=!0){let i=t[j];if(EC(i,t,e,n),r){let s=Nc(n,e),a=t[Te],l=Gg(a,e[jn]);l!==null&&wC(i,e[mt],a,t,l,s)}let o=t[hs];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function Ss(e,t,n,r,i=!1){for(;n!==null;){if(n.type===128){n=i?n.projectionNext:n.next;continue}let o=t[n.index];o!==null&&r.push(At(o)),$t(o)&&pw(o,r);let s=n.type;if(s&8)Ss(e,t,n.child,r);else if(s&32){let a=bu(n,t),l;for(;l=a();)r.push(l)}else if(s&16){let a=Wg(t,n);if(Array.isArray(a))r.push(...a);else{let l=Bn(t[Mt]);Ss(l[j],l,a,r,!0)}}n=i?n.projectionNext:n.next}return r}function pw(e,t){for(let n=nt;n<e.length;n++){let r=e[n],i=r[j].firstChild;i!==null&&Ss(r[j],r,i,t)}e[jn]!==e[Bt]&&t.push(e[jn])}var dm=[];function gw(e){return e[tt]??mw(e)}function mw(e){let t=dm.pop()??Object.create(vw);return t.lView=e,t}function yw(e){e.lView[tt]!==e&&(e.lView=null,dm.push(e))}var vw=Z(E({},di),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Gs(e.lView)},consumerOnSignalRead(){this.lView[tt]=this}});function Cw(e){let t=e[tt]??Object.create(ww);return t.lView=e,t}var ww=Z(E({},di),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=Bn(e.lView);for(;t&&!fm(t[j]);)t=Bn(t);t&&Jp(t)},consumerOnSignalRead(){this.lView[tt]=this}});function fm(e){return e.type!==2}var Dw=100;function hm(e,t=!0,n=0){let r=e[It],i=r.rendererFactory,o=!1;o||i.begin?.();try{Ew(e,n)}catch(s){throw t&&cm(e,s),s}finally{o||(i.end?.(),r.inlineEffectRunner?.flush())}}function Ew(e,t){let n=og();try{jh(!0),Uc(e,t);let r=0;for(;Ei(e);){if(r===Dw)throw new C(103,!1);r++,Uc(e,1)}}finally{jh(n)}}function bw(e,t,n,r){let i=t[R];if((i&256)===256)return;let o=!1,s=!1;!o&&t[It].inlineEffectRunner?.flush(),cu(t);let a=!0,l=null,c=null;o||(fm(e)?(c=gw(t),l=Ao(c)):zf()===null?(a=!1,c=Cw(t),l=Ao(c)):t[tt]&&(Ol(t[tt]),t[tt]=null));try{Xp(t),C2(e.bindingStartIndex),n!==null&&Jg(e,t,n,2,r);let u=(i&3)===3;if(!o)if(u){let f=e.preOrderCheckHooks;f!==null&&ss(t,f,null)}else{let f=e.preOrderHooks;f!==null&&as(t,f,0,null),tc(t,0)}if(s||_w(t),pm(t,0),e.contentQueries!==null&&lm(e,t),!o)if(u){let f=e.contentCheckHooks;f!==null&&ss(t,f)}else{let f=e.contentHooks;f!==null&&as(t,f,1),tc(t,1)}PC(e,t);let d=e.components;d!==null&&mm(t,d,0);let g=e.viewQuery;if(g!==null&&Oc(2,g,r),!o)if(u){let f=e.viewCheckHooks;f!==null&&ss(t,f)}else{let f=e.viewHooks;f!==null&&as(t,f,2),tc(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ec]){for(let f of t[ec])f();t[ec]=null}o||(t[R]&=-73)}catch(u){throw o||Gs(t),u}finally{c!==null&&(Nl(c,l),a&&yw(c)),uu()}}function pm(e,t){for(let n=Fg(e);n!==null;n=Ng(n))for(let r=nt;r<n.length;r++){let i=n[r];gm(i,t)}}function _w(e){for(let t=Fg(e);t!==null;t=Ng(t)){if(!(t[R]&ys.HasTransplantedViews))continue;let n=t[ms];for(let r=0;r<n.length;r++){let i=n[r];Jp(i)}}}function Iw(e,t,n){let r=wn(t,e);gm(r,n)}function gm(e,t){ou(e)&&Uc(e,t)}function Uc(e,t){let r=e[j],i=e[R],o=e[tt],s=!!(t===0&&i&16);if(s||=!!(i&64&&t===0),s||=!!(i&1024),s||=!!(o?.dirty&&Rl(o)),s||=!1,o&&(o.dirty=!1),e[R]&=-9217,s)bw(r,e,r.template,e[pt]);else if(i&8192){pm(e,1);let a=r.components;a!==null&&mm(e,a,1)}}function mm(e,t,n){for(let r=0;r<t.length;r++)Iw(e,t[r],n)}function xu(e,t){let n=og()?64:1088;for(e[It].changeDetectionScheduler?.notify(t);e;){e[R]|=n;let r=Bn(e);if(Cc(e)&&!r)return e;e=r}return null}var Gn=class{get rootNodes(){let t=this._lView,n=t[j];return Ss(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[pt]}set context(t){this._lView[pt]=t}get destroyed(){return(this._lView[R]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Ue];if($t(t)){let n=t[gs],r=n?n.indexOf(this):-1;r>-1&&(Fc(t,r),ds(n,r))}this._attachedToViewContainer=!1}Hg(this._lView[j],this._lView)}onDestroy(t){eg(this._lView,t)}markForCheck(){xu(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[R]&=-129}reattach(){Dc(this._lView),this._lView[R]|=128}detectChanges(){this._lView[R]|=1024,hm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Cc(this._lView),n=this._lView[Ar];n!==null&&!t&&_u(n,this._lView),Bg(this._lView[j],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t;let n=Cc(this._lView),r=this._lView[Ar];r!==null&&!n&&$g(r,this._lView),Dc(this._lView)}},ea=(()=>{class e{static{this.__NG_ELEMENT_ID__=Aw}}return e})(),Mw=ea,Sw=class extends Mw{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let i=fw(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Gn(i)}};function Aw(){return Tw($e(),re())}function Tw(e,t){return e.type&4?new Sw(t,e,qs(e,t)):null}var iT=new RegExp(`^(\\d+)*(${aC}|${sC})*(.*)`);var xw=()=>null;function tp(e,t){return xw(e,t)}var Fr=class{},ym=new I("",{providedIn:"root",factory:()=>!1});var vm=new I(""),Pc=class{},As=class{};function Fw(e){let t=Error(`No component factory found for ${Re(e)}.`);return t[Nw]=e,t}var Nw="ngComponent";var kc=class{resolveComponentFactory(t){throw Fw(t)}},Nr=class{static{this.NULL=new kc}},vn=class{},jr=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>Rw()}}return e})();function Rw(){let e=re(),t=$e(),n=wn(t.index,e);return(pn(n)?n:e)[Te]}var Ow=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>null})}}return e})();var np=new Set;function Kn(e){np.has(e)||(np.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Be=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Be||{}),Uw={destroy(){}};function Fu(e,t){!t&&Jv(Fu);let n=t?.injector??w(We);return gC(n)?(Kn("NgAfterNextRender"),kw(e,n,!0,t?.phase??Be.MixedReadWrite)):Uw}function Pw(e,t){if(e instanceof Function)switch(t){case Be.EarlyRead:return{earlyRead:e};case Be.Write:return{write:e};case Be.MixedReadWrite:return{mixedReadWrite:e};case Be.Read:return{read:e}}return e}function kw(e,t,n,r){let i=Pw(e,r),o=t.get(Nu),s=o.handler??=new Vc,a=[],l=[],c=()=>{for(let f of l)s.unregister(f);u()},u=t.get(mu).onDestroy(c),d=0,g=(f,m)=>{if(!m)return;let y=n?(...S)=>(d--,d<1&&c(),m(...S)):m,D=qe(t,()=>new Lc(f,a,y));s.register(D),l.push(D),d++};return g(Be.EarlyRead,i.earlyRead),g(Be.Write,i.write),g(Be.MixedReadWrite,i.mixedReadWrite),g(Be.Read,i.read),{destroy:c}}var Lc=class{constructor(t,n,r){this.phase=t,this.pipelinedArgs=n,this.callbackFn=r,this.zone=w(X),this.errorHandler=w(Vt,{optional:!0}),w(Fr,{optional:!0})?.notify(6)}invoke(){try{let t=this.zone.runOutsideAngular(()=>this.callbackFn.apply(null,this.pipelinedArgs));this.pipelinedArgs.splice(0,this.pipelinedArgs.length,t)}catch(t){this.errorHandler?.handleError(t)}}},Vc=class{constructor(){this.executingCallbacks=!1,this.buckets={[Be.EarlyRead]:new Set,[Be.Write]:new Set,[Be.MixedReadWrite]:new Set,[Be.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},Nu=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})();function jc(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")o=a;else if(o==1)i=Ah(i,a);else if(o==2){let l=a,c=t[++s];r=Ah(r,l+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}var Ts=class extends Nr{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=mn(t);return new Rr(n,this.ngModule)}};function rp(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let i=e[r];if(i===void 0)continue;let o=Array.isArray(i),s=o?i[0]:i,a=o?i[1]:gn.None;t?n.push({propName:s,templateName:r,isSignal:(a&gn.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function Lw(e){let t=e.toLowerCase();return t==="svg"?Kp:t==="math"?o2:null}var Rr=class extends As{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=rp(t.inputs,!0);if(n!==null)for(let i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return rp(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=kv(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=K(null);try{i=i||this.ngModule;let s=i instanceof Oe?i:i?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new bc(t,s):t,l=a.get(vn,null);if(l===null)throw new C(407,!1);let c=a.get(Ow,null),u=a.get(Nu,null),d=a.get(Fr,null),g={rendererFactory:l,sanitizer:c,inlineEffectRunner:null,afterRenderEventManager:u,changeDetectionScheduler:d},f=l.createRenderer(null,this.componentDef),m=this.componentDef.selectors[0][0]||"div",y=r?VC(f,r,this.componentDef.encapsulation,a):jg(f,m,Lw(m)),D=512;this.componentDef.signals?D|=4096:this.componentDef.onPush||(D|=16);let S=null;y!==null&&(S=Du(y,a,!0));let H=Su(0,null,null,1,0,null,null,null,null,null,null),A=Ys(null,H,null,D,null,null,g,f,a,null,S);cu(A);let W,ce;try{let te=this.componentDef,se,be=null;te.findHostDirectiveDefs?(se=[],be=new Map,te.findHostDirectiveDefs(te,se,be),se.push(te)):se=[te];let Ut=Vw(A,y),on=jw(Ut,y,te,se,A,g,f);ce=Yp(H,St),y&&Hw(f,te,y,r),n!==void 0&&zw(ce,this.ngContentSelectors,n),W=$w(on,te,se,be,A,[Gw]),Tu(H,A,null)}finally{uu()}return new Bc(this.componentType,W,qs(ce,A),A,ce)}finally{K(o)}}},Bc=class extends Pc{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new Gn(i,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,i;if(r!==null&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView;Au(o[j],o,i,t,n),this.previousInputValues.set(t,n);let s=wn(this._tNode.index,o);xu(s,1)}}get injector(){return new Ln(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Vw(e,t){let n=e[j],r=St;return e[r]=t,Xs(n,r,2,"#host",null)}function jw(e,t,n,r,i,o,s){let a=i[j];Bw(r,e,t,s);let l=null;t!==null&&(l=Du(t,i[Sr]));let c=o.rendererFactory.createRenderer(t,n),u=16;n.signals?u=4096:n.onPush&&(u=64);let d=Ys(i,rm(n),null,u,i[e.index],e,o,c,null,null,l);return a.firstCreatePass&&Rc(a,e,r.length-1),Js(i,d),i[e.index]=d}function Bw(e,t,n,r){for(let i of e)t.mergedAttrs=Ci(t.mergedAttrs,i.hostAttrs);t.mergedAttrs!==null&&(jc(t,t.mergedAttrs,!0),n!==null&&Zg(r,n,t))}function $w(e,t,n,r,i,o){let s=$e(),a=i[j],l=rt(s,i);om(a,i,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,g=xr(i,a,d,s);zn(g,i)}sm(a,i,s),l&&zn(l,i);let c=xr(i,a,s.directiveStart+s.componentOffset,s);if(e[pt]=i[pt]=c,o!==null)for(let u of o)u(c,t);return em(a,s,i),c}function Hw(e,t,n,r){if(r)gc(e,n,["ng-version","18.1.4"]);else{let{attrs:i,classes:o}=Lv(t.selectors[0]);i&&gc(e,n,i),o&&o.length>0&&qg(e,n,o.join(" "))}}function zw(e,t,n){let r=e.projection=[];for(let i=0;i<t.length;i++){let o=n[i];r.push(o!=null?Array.from(o):null)}}function Gw(){let e=$e();hu(re()[j],e)}var Br=(()=>{class e{static{this.__NG_ELEMENT_ID__=Ww}}return e})();function Ww(){let e=$e();return Zw(e,re())}var qw=Br,Cm=class extends qw{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return qs(this._hostTNode,this._hostLView)}get injector(){return new Ln(this._hostTNode,this._hostLView)}get parentInjector(){let t=pu(this._hostTNode,this._hostLView);if(yg(t)){let n=ws(t,this._hostLView),r=Cs(t),i=n[j].data[r+8];return new Ln(i,n)}else return new Ln(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=ip(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-nt}createEmbeddedView(t,n,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let s=tp(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},o,s);return this.insertImpl(a,i,ep(this._hostTNode,s)),a}createComponent(t,n,r,i,o){let s=t&&!t2(t),a;if(s)a=n;else{let m=n||{};a=m.index,r=m.injector,i=m.projectableNodes,o=m.environmentInjector||m.ngModuleRef}let l=s?t:new Rr(mn(t)),c=r||this.parentInjector;if(!o&&l.ngModule==null){let y=(s?c:this.parentInjector).get(Oe,null);y&&(o=y)}let u=mn(l.componentType??{}),d=tp(this._lContainer,u?.id??null),g=d?.firstChild??null,f=l.create(c,i,g,o);return this.insertImpl(f.hostView,a,ep(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let i=t._lView;if(a2(i)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let l=i[Ue],c=new Cm(l,l[mt],l[Ue]);c.detach(c.indexOf(t))}}let o=this._adjustIndex(n),s=this._lContainer;return hw(s,i,o,r),t.attachToViewContainerRef(),_p(sc(s),o,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=ip(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Fc(this._lContainer,n);r&&(ds(sc(this._lContainer),n),Hg(r[j],r))}detach(t){let n=this._adjustIndex(t,-1),r=Fc(this._lContainer,n);return r&&ds(sc(this._lContainer),n)!=null?new Gn(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function ip(e){return e[gs]}function sc(e){return e[gs]||(e[gs]=[])}function Zw(e,t){let n,r=t[e.index];return $t(r)?n=r:(n=am(r,t,null,e),t[e.index]=n,Js(t,n)),Qw(n,t,e,r),new Cm(n,e,t)}function Kw(e,t){let n=e[Te],r=n.createComment(""),i=rt(t,e),o=Gg(n,i);return Ms(n,o,r,SC(n,i),!1),r}var Qw=Jw,Yw=()=>!1;function Xw(e,t,n){return Yw(e,t,n)}function Jw(e,t,n,r){if(e[jn])return;let i;n.type&8?i=At(r):i=Kw(t,n),e[jn]=i}function Ti(e,t){Kn("NgSignals");let n=th(e),r=n[an];return t?.equal&&(r.equal=t.equal),n.set=i=>Ul(r,i),n.update=i=>nh(r,i),n.asReadonly=eD.bind(n),n}function eD(){let e=this[an];if(e.readonlyFn===void 0){let t=()=>this();t[an]=e,e.readonlyFn=t}return e.readonlyFn}function tD(e){let t=[],n=new Map;function r(i){let o=n.get(i);if(!o){let s=e(i);n.set(i,o=s.then(oD))}return o}return xs.forEach((i,o)=>{let s=[];i.templateUrl&&s.push(r(i.templateUrl).then(c=>{i.template=c}));let a=typeof i.styles=="string"?[i.styles]:i.styles||[];if(i.styles=a,i.styleUrl&&i.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(i.styleUrls?.length){let c=i.styles.length,u=i.styleUrls;i.styleUrls.forEach((d,g)=>{a.push(""),s.push(r(d).then(f=>{a[c+g]=f,u.splice(u.indexOf(d),1),u.length==0&&(i.styleUrls=void 0)}))})}else i.styleUrl&&s.push(r(i.styleUrl).then(c=>{a.push(c),i.styleUrl=void 0}));let l=Promise.all(s).then(()=>sD(o));t.push(l)}),rD(),Promise.all(t).then(()=>{})}var xs=new Map,nD=new Set;function rD(){let e=xs;return xs=new Map,e}function iD(){return xs.size===0}function oD(e){return typeof e=="string"?e:e.text()}function sD(e){nD.delete(e)}function aD(e){return Object.getPrototypeOf(e.prototype).constructor}function ta(e){let t=aD(e.type),n=!0,r=[e];for(;t;){let i;if(yn(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);let s=e;s.inputs=ns(e.inputs),s.inputTransforms=ns(e.inputTransforms),s.declaredInputs=ns(e.declaredInputs),s.outputs=ns(e.outputs);let a=i.hostBindings;a&&fD(e,a);let l=i.viewQuery,c=i.contentQueries;if(l&&uD(e,l),c&&dD(e,c),lD(e,i),ov(e.outputs,i.outputs),yn(i)&&i.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let s=0;s<o.length;s++){let a=o[s];a&&a.ngInherit&&a(e),a===ta&&(n=!1)}}t=Object.getPrototypeOf(t)}cD(r)}function lD(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let i=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(i))continue;e.inputTransforms??={},e.inputTransforms[i]=t.inputTransforms[i]}}}function cD(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=Ci(i.hostAttrs,n=Ci(n,i.hostAttrs))}}function ns(e){return e===_r?{}:e===et?[]:e}function uD(e,t){let n=e.viewQuery;n?e.viewQuery=(r,i)=>{t(r,i),n(r,i)}:e.viewQuery=t}function dD(e,t){let n=e.contentQueries;n?e.contentQueries=(r,i,o)=>{t(r,i,o),n(r,i,o)}:e.contentQueries=t}function fD(e,t){let n=e.hostBindings;n?e.hostBindings=(r,i)=>{t(r,i),n(r,i)}:e.hostBindings=t}function Ru(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let i=t[r];Array.isArray(i)&&i[3]&&(n[r]=i[3])}e.inputTransforms=n}var Cn=class{},bi=class{};var Fs=class extends Cn{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Ts(this);let i=Op(t);this._bootstrapComponents=Vg(i.bootstrap),this._r3Injector=Mg(t,n,[{provide:Cn,useValue:this},{provide:Nr,useValue:this.componentFactoryResolver},...r],Re(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ns=class extends bi{constructor(t){super(),this.moduleType=t}create(t){return new Fs(this.moduleType,t,[])}};function hD(e,t,n){return new Fs(e,t,n)}var $c=class extends Cn{constructor(t){super(),this.componentFactoryResolver=new Ts(this),this.instance=null;let n=new wi([...t.providers,{provide:Cn,useValue:this},{provide:Nr,useValue:this.componentFactoryResolver}],t.parent||ru(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function na(e,t,n=null){return new $c({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function wm(e){return gD(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function pD(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function gD(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function mD(e,t,n){return e[t]=n}function Or(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Dm(e,t,n,r){let i=Or(e,t,n);return Or(e,t+1,r)||i}function yD(e){return(e.flags&32)===32}function vD(e,t,n,r,i,o,s,a,l){let c=t.consts,u=Xs(t,e,4,s||null,a||null);im(t,n,u,vs(c,l)),hu(t,u);let d=u.tView=Su(2,u,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function CD(e,t,n,r,i,o,s,a,l,c){let u=n+St,d=t.firstCreatePass?vD(u,t,e,r,i,o,s,a,l):t.data[u];Si(d,!1);let g=wD(t,e,d,n);du()&&Iu(t,e,g,d),zn(g,e);let f=am(g,e,g,d);return e[u]=f,Js(e,f),Xw(f,d,e),iu(d)&&tm(t,e,d),l!=null&&nm(e,d,c),d}function ot(e,t,n,r,i,o,s,a){let l=re(),c=it(),u=vs(c.consts,o);return CD(l,c,e,t,n,r,i,u,s,a),ot}var wD=DD;function DD(e,t,n,r){return fu(!0),t[Te].createComment("")}function ra(e,t,n,r){let i=re(),o=lu();if(Or(i,o,t)){let s=it(),a=pg();rw(a,i,e,t,n,r)}return ra}function ED(e,t,n,r){return Or(e,lu(),n)?t+br(n)+r:Gt}function bD(e,t,n,r,i,o){let s=v2(),a=Dm(e,s,n,i);return ag(2),a?t+br(n)+r+br(i)+o:Gt}function rs(e,t){return e<<17|t<<2}function Wn(e){return e>>17&32767}function _D(e){return(e&2)==2}function ID(e,t){return e&131071|t<<17}function Hc(e){return e|2}function Ur(e){return(e&131068)>>2}function ac(e,t){return e&-131069|t<<2}function MD(e){return(e&1)===1}function zc(e){return e|1}function SD(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,a=Wn(s),l=Ur(s);e[r]=n;let c=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Mi(d,u)>0)&&(c=!0)}else u=n;if(i)if(l!==0){let g=Wn(e[a+1]);e[r+1]=rs(g,a),g!==0&&(e[g+1]=ac(e[g+1],r)),e[a+1]=ID(e[a+1],r)}else e[r+1]=rs(a,0),a!==0&&(e[a+1]=ac(e[a+1],r)),a=r;else e[r+1]=rs(l,0),a===0?a=r:e[l+1]=ac(e[l+1],r),l=r;c&&(e[r+1]=Hc(e[r+1])),op(e,u,r,!0),op(e,u,r,!1),AD(t,u,e,r,o),s=rs(a,l),o?t.classBindings=s:t.styleBindings=s}function AD(e,t,n,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof t=="string"&&Mi(o,t)>=0&&(n[r+1]=zc(n[r+1]))}function op(e,t,n,r){let i=e[n+1],o=t===null,s=r?Wn(i):Ur(i),a=!1;for(;s!==0&&(a===!1||o);){let l=e[s],c=e[s+1];TD(l,t)&&(a=!0,e[s+1]=r?zc(c):Hc(c)),s=r?Wn(c):Ur(c)}a&&(e[n+1]=r?Hc(i):zc(i))}function TD(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Mi(e,t)>=0:!1}function me(e,t,n){let r=re(),i=lu();if(Or(r,i,t)){let o=it(),s=pg();GC(o,s,r,e,t,r[Te],n,!1)}return me}function sp(e,t,n,r,i){let o=t.inputs,s=i?"class":"style";Au(e,n,o[s],s,r)}function Ou(e,t){return xD(e,t,null,!0),Ou}function xD(e,t,n,r){let i=re(),o=it(),s=ag(2);if(o.firstUpdatePass&&ND(o,e,s,r),t!==Gt&&Or(i,s,t)){let a=o.data[qn()];kD(o,a,i,i[Te],e,i[s+1]=LD(t,n),r,s)}}function FD(e,t){return t>=e.expandoStartIndex}function ND(e,t,n,r){let i=e.data;if(i[n+1]===null){let o=i[qn()],s=FD(e,n);VD(o,r)&&t===null&&!s&&(t=!1),t=RD(i,o,t,r),SD(i,o,t,n,s,r)}}function RD(e,t,n,r){let i=b2(e),o=r?t.residualClasses:t.residualStyles;if(i===null)(r?t.classBindings:t.styleBindings)===0&&(n=lc(null,e,t,n,r),n=_i(n,t.attrs,r),o=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==i)if(n=lc(i,e,t,n,r),o===null){let l=OD(e,t,r);l!==void 0&&Array.isArray(l)&&(l=lc(null,e,t,l[1],r),l=_i(l,t.attrs,r),UD(e,t,r,l))}else o=PD(e,t,r)}return o!==void 0&&(r?t.residualClasses=o:t.residualStyles=o),n}function OD(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Ur(r)!==0)return e[Wn(r)]}function UD(e,t,n,r){let i=n?t.classBindings:t.styleBindings;e[Wn(i)]=r}function PD(e,t,n){let r,i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++){let s=e[o].hostAttrs;r=_i(r,s,n)}return _i(r,t.attrs,n)}function lc(e,t,n,r,i){let o=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(o=t[a],r=_i(r,o.hostAttrs,i),o!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function _i(e,t,n){let r=n?1:2,i=-1;if(t!==null)for(let o=0;o<t.length;o++){let s=t[o];typeof s=="number"?i=s:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Iv(e,s,n?!0:t[++o]))}return e===void 0?null:e}function kD(e,t,n,r,i,o,s,a){if(!(t.type&3))return;let l=e.data,c=l[a+1],u=MD(c)?ap(l,t,n,i,Ur(c),s):void 0;if(!Rs(u)){Rs(o)||_D(c)&&(o=ap(l,null,n,i,a,s));let d=Qp(qn(),n);OC(r,s,d,i,o)}}function ap(e,t,n,r,i,o){let s=t===null,a;for(;i>0;){let l=e[i],c=Array.isArray(l),u=c?l[1]:l,d=u===null,g=n[i+1];g===Gt&&(g=d?et:void 0);let f=d?Xl(g,r):u===r?g:void 0;if(c&&!Rs(f)&&(f=Xl(l,r)),Rs(f)&&(a=f,s))return a;let m=e[i+1];i=s?Wn(m):Ur(m)}if(t!==null){let l=o?t.residualClasses:t.residualStyles;l!=null&&(a=Xl(l,r))}return a}function Rs(e){return e!==void 0}function LD(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Re(Ai(e)))),e}function VD(e,t){return(e.flags&(t?8:16))!==0}function jD(e,t,n,r,i,o){let s=t.consts,a=vs(s,i),l=Xs(t,e,2,r,a);return im(t,n,l,vs(s,o)),l.attrs!==null&&jc(l,l.attrs,!1),l.mergedAttrs!==null&&jc(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function h(e,t,n,r){let i=re(),o=it(),s=St+e,a=i[Te],l=o.firstCreatePass?jD(s,o,i,t,n,r):o.data[s],c=BD(o,i,l,a,t,e);i[s]=c;let u=iu(l);return Si(l,!0),Zg(a,c,l),!yD(l)&&du()&&Iu(o,i,c,l),u2()===0&&zn(c,i),d2(),u&&(tm(o,i,l),em(o,l,i)),r!==null&&nm(i,l),h}function p(){let e=$e();ig()?y2():(e=e.parent,Si(e,!1));let t=e;p2(t)&&g2(),f2();let n=it();return n.firstCreatePass&&(hu(n,e),zp(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&F2(t)&&sp(n,t,re(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&N2(t)&&sp(n,t,re(),t.stylesWithoutHost,!1),p}function v(e,t,n,r){return h(e,t,n,r),p(),v}var BD=(e,t,n,r,i,o)=>(fu(!0),jg(r,i,S2()));function Em(){return re()}var kn=void 0;function $D(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var HD=["en",[["a","p"],["AM","PM"],kn],[["AM","PM"],kn,kn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],kn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],kn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",kn,"{1} 'at' {0}",kn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",$D],cc={};function st(e){let t=zD(e),n=lp(t);if(n)return n;let r=t.split("-")[0];if(n=lp(r),n)return n;if(r==="en")return HD;throw new C(701,!1)}function lp(e){return e in cc||(cc[e]=Fe.ng&&Fe.ng.common&&Fe.ng.common.locales&&Fe.ng.common.locales[e]),cc[e]}var ue=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ue||{});function zD(e){return e.toLowerCase().replace(/_/g,"-")}var Os="en-US";var GD=Os;function WD(e){typeof e=="string"&&(GD=e.toLowerCase().replace(/_/g,"-"))}var qD=(e,t,n)=>{};function Wt(e,t,n,r){let i=re(),o=it(),s=$e();return KD(o,i,i[Te],s,e,t,r),Wt}function ZD(e,t,n,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let s=i[o];if(s===n&&i[o+1]===r){let a=t[ps],l=i[o+2];return a.length>l?a[l]:null}typeof s=="string"&&(o+=2)}return null}function KD(e,t,n,r,i,o,s){let a=iu(r),c=e.firstCreatePass&&lw(e),u=t[pt],d=aw(t),g=!0;if(r.type&3||s){let y=rt(r,t),D=s?s(y):y,S=d.length,H=s?W=>s(At(W[r.index])):r.index,A=null;if(!s&&a&&(A=ZD(e,t,i,r.index)),A!==null){let W=A.__ngLastListenerFn__||A;W.__ngNextListenerFn__=o,A.__ngLastListenerFn__=o,g=!1}else{o=up(r,t,u,o),qD(y,i,o);let W=n.listen(D,i,o);d.push(o,W),c&&c.push(i,H,S,S+1)}}else o=up(r,t,u,o);let f=r.outputs,m;if(g&&f!==null&&(m=f[i])){let y=m.length;if(y)for(let D=0;D<y;D+=2){let S=m[D],H=m[D+1],ce=t[S][H].subscribe(o),te=d.length;d.push(o,ce),c&&c.push(i,r.index,te,-(te+1))}}}function cp(e,t,n,r){let i=K(null);try{return Et(6,t,n),n(r)!==!1}catch(o){return cm(e,o),!1}finally{Et(7,t,n),K(i)}}function up(e,t,n,r){return function i(o){if(o===Function)return r;let s=e.componentOffset>-1?wn(e.index,t):t;xu(s,5);let a=cp(t,n,r,o),l=i.__ngNextListenerFn__;for(;l;)a=cp(t,n,l,o)&&a,l=l.__ngNextListenerFn__;return a}}function qt(e=1){return I2(e)}function QD(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function N(e,t=""){let n=re(),r=it(),i=e+St,o=r.firstCreatePass?Xs(r,i,1,t,null):r.data[i],s=YD(r,n,o,t,e);n[i]=s,du()&&Iu(r,n,s,o),Si(o,!1)}var YD=(e,t,n,r,i)=>(fu(!0),yC(t[Te],r));function at(e){return Dn("",e,""),at}function Dn(e,t,n){let r=re(),i=ED(r,e,t,n);return i!==Gt&&um(r,qn(),i),Dn}function ia(e,t,n,r,i){let o=re(),s=bD(o,e,t,n,r,i);return s!==Gt&&um(o,qn(),s),ia}function XD(e,t,n){let r=it();if(r.firstCreatePass){let i=yn(e);Gc(n,r.data,r.blueprint,i,!0),Gc(t,r.data,r.blueprint,i,!1)}}function Gc(e,t,n,r,i){if(e=Ne(e),Array.isArray(e))for(let o=0;o<e.length;o++)Gc(e[o],t,n,r,i);else{let o=it(),s=re(),a=$e(),l=Mr(e)?e:Ne(e.provide),c=jp(e),u=a.providerIndexes&1048575,d=a.directiveStart,g=a.providerIndexes>>20;if(Mr(e)||!e.multi){let f=new Hn(c,i,$),m=dc(l,t,i?u:u+g,d);m===-1?(Ic(Es(a,s),o,l),uc(o,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[m]=f,s[m]=f)}else{let f=dc(l,t,u+g,d),m=dc(l,t,u,u+g),y=f>=0&&n[f],D=m>=0&&n[m];if(i&&!D||!i&&!y){Ic(Es(a,s),o,l);let S=tE(i?eE:JD,n.length,i,r,c);!i&&D&&(n[m].providerFactory=S),uc(o,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(S),s.push(S)}else{let S=bm(n[i?m:f],c,!i&&r);uc(o,e,f>-1?f:m,S)}!i&&r&&D&&n[m].componentProviders++}}}function uc(e,t,n,r){let i=Mr(t),o=Wv(t);if(i||o){let l=(o?Ne(t.useClass):t).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){let u=c.indexOf(n);u===-1?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function bm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function dc(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function JD(e,t,n,r){return Wc(this.multi,[])}function eE(e,t,n,r){let i=this.multi,o;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=xr(n,n[j],this.providerFactory.index,r);o=a.slice(0,s),Wc(i,o);for(let l=s;l<a.length;l++)o.push(a[l])}else o=[],Wc(i,o);return o}function Wc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function tE(e,t,n,r,i){let o=new Hn(e,n,$);return o.multi=[],o.index=t,o.componentProviders=0,bm(o,i,r&&!n),o}function _m(e,t=[]){return n=>{n.providersResolver=(r,i)=>XD(r,i?i(e):e,t)}}var nE=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=kp(!1,n.type),i=r.length>0?na([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(_(Oe))})}}return e})();function Im(e){Kn("NgStandalone"),e.getStandaloneInjector=t=>t.get(nE).getOrCreateStandaloneInjector(e)}function Mm(e,t,n,r,i){return Sm(re(),sg(),e,t,n,r,i)}function rE(e,t){let n=e[t];return n===Gt?void 0:n}function Sm(e,t,n,r,i,o,s){let a=t+n;return Dm(e,a,i,o)?mD(e,a+2,s?r.call(s,i,o):r(i,o)):rE(e,a+2)}function xi(e,t){let n=it(),r,i=e+St;n.firstCreatePass?(r=iE(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];let o=r.factory||(r.factory=Vn(r.type,!0)),s,a=je($);try{let l=Ds(!1),c=o();return Ds(l),QD(n,re(),i,c),c}finally{je(a)}}function iE(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function Fi(e,t,n,r){let i=e+St,o=re(),s=s2(o,i);return oE(o,i)?Sm(o,sg(),t,s.transform,n,r,s):s.transform(n,r)}function oE(e,t){return e[j].data[t].pure}var is=null;function sE(e){is!==null&&(e.defaultEncapsulation!==is.defaultEncapsulation||e.preserveWhitespaces!==is.preserveWhitespaces)||(is=e)}var oa=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Uu=new I(""),Ni=new I(""),sa=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,Pu||(aE(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{X.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n()},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(_(X),_(aa),_(Ni))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),aa=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Pu?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function aE(e){Pu=e}var Pu;function Qn(e){return!!e&&typeof e.then=="function"}function Am(e){return!!e&&typeof e.subscribe=="function"}var la=new I(""),Tm=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=w(la,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let o=i();if(Qn(o))n.push(o);else if(Am(o)){let s=new Promise((a,l)=>{o.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ca=new I("");function lE(){eh(()=>{throw new C(600,!1)})}function cE(e){return e.isBoundToModule}var uE=10;function dE(e,t,n){try{let r=n();return Qn(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function xm(e,t){return Array.isArray(t)?t.reduce(xm,e):E(E({},e),t)}var En=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=w(X2),this.afterRenderEffectManager=w(Nu),this.zonelessEnabled=w(ym),this.externalTestViews=new Set,this.beforeRender=new Ie,this.afterTick=new Ie,this.componentTypes=[],this.components=[],this.isStable=w(Ht).hasPendingTasks.pipe(P(n=>!n)),this._injector=w(Oe)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let i=n instanceof As;if(!this._injector.get(Tm).done){let g=!i&&Rp(n),f=!1;throw new C(405,f)}let s;i?s=n:s=this._injector.get(Nr).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=cE(s)?void 0:this._injector.get(Cn),l=r||s.selector,c=s.create(We.NULL,[],l,a),u=c.location.nativeElement,d=c.injector.get(Uu,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),ls(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new C(101,!1);let r=K(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(i){this.internalErrorHandler(i)}finally{this._runningTick=!1,K(r),this.afterTick.next()}}detectChangesInAttachedViews(n){let r=null;this._injector.destroyed||(r=this._injector.get(vn,null,{optional:!0}));let i=0,o=this.afterRenderEffectManager;for(;i<uE;){let s=i===0;if(n||!s){this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:l}of this._views)fE(a,l,s,this.zonelessEnabled)}else r?.begin?.(),r?.end?.();if(i++,o.executeInternalCallbacks(),!this.allViews.some(({_lView:a})=>Ei(a))&&(o.execute(),!this.allViews.some(({_lView:a})=>Ei(a))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ls(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(ca,[]);[...this._bootstrapListeners,...r].forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ls(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function ls(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function fE(e,t,n,r){if(!n&&!Ei(e))return;hm(e,t,n&&!r?0:1)}var qc=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ua=(()=>{class e{compileModuleSync(n){return new Ns(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),i=Op(n),o=Vg(i.declarations).reduce((s,a)=>{let l=mn(a);return l&&s.push(new Rr(l)),s},[]);return new qc(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),hE=new I("");function pE(e,t,n){let r=new Ns(n);return Promise.resolve(r)}function dp(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var gE=(()=>{class e{constructor(){this.zone=w(X),this.changeDetectionScheduler=w(Fr),this.applicationRef=w(En)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function mE({ngZoneFactory:e,ignoreChangesOutsideZone:t}){return e??=()=>new X(Fm()),[{provide:X,useFactory:e},{provide:Ir,multi:!0,useFactory:()=>{let n=w(gE,{optional:!0});return()=>n.initialize()}},{provide:Ir,multi:!0,useFactory:()=>{let n=w(yE);return()=>{n.initialize()}}},t===!0?{provide:vm,useValue:!0}:[]]}function Fm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var yE=(()=>{class e{constructor(){this.subscription=new fe,this.initialized=!1,this.zone=w(X),this.pendingTasks=w(Ht)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{X.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{X.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var vE=(()=>{class e{constructor(){this.appRef=w(En),this.taskService=w(Ht),this.ngZone=w(X),this.zonelessEnabled=w(ym),this.disableScheduling=w(vm,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new fe,this.cancelScheduledCallback=null,this.shouldRefreshViews=!1,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof _s||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 3:case 2:case 0:case 4:case 5:case 1:{this.shouldRefreshViews=!0;break}case 8:case 7:case 6:case 9:default:}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?Gh:Sg;this.pendingRenderTaskId=this.taskService.add(),this.zoneIsDefined?Zone.root.run(()=>{this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}):this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&X.isInAngularZone())}tick(n){if(this.runningTick||this.appRef.destroyed)return;let r=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick(n)},void 0,this.schedulerTickApplyArgs)}catch(i){throw this.taskService.remove(r),i}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Gh(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(r)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.shouldRefreshViews=!1,this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function CE(){return typeof $localize<"u"&&$localize.locale||Os}var da=new I("",{providedIn:"root",factory:()=>w(da,V.Optional|V.SkipSelf)||CE()});var Nm=new I(""),Rm=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let i=Y2(r?.ngZone,Fm({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{let o=r?.ignoreChangesOutsideZone,s=hD(n.moduleType,this.injector,[...mE({ngZoneFactory:()=>i,ignoreChangesOutsideZone:o}),{provide:Fr,useExisting:vE}]),a=s.injector.get(Vt,null);return i.runOutsideAngular(()=>{let l=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{ls(this._modules,s),l.unsubscribe()})}),dE(a,i,()=>{let l=s.injector.get(Tm);return l.runInitializers(),l.donePromise.then(()=>{let c=s.injector.get(da,Os);return WD(c||Os),this._moduleDoBootstrap(s),s})})})}bootstrapModule(n,r=[]){let i=xm({},r);return pE(this.injector,i,n).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(n){let r=n.injector.get(En);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(r);else throw new C(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Nm,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(_(We))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),yi=null,Om=new I("");function wE(e){if(yi&&!yi.get(Om,!1))throw new C(400,!1);lE(),yi=e;let t=e.get(Rm);return bE(e),t}function ku(e,t,n=[]){let r=`Platform: ${t}`,i=new I(r);return(o=[])=>{let s=Um();if(!s||s.injector.get(Om,!1)){let a=[...n,...o,{provide:i,useValue:!0}];e?e(a):wE(DE(a,r))}return EE(i)}}function DE(e=[],t){return We.create({name:t,providers:[{provide:$s,useValue:"platform"},{provide:Nm,useValue:new Set([()=>yi=null])},...e]})}function EE(e){let t=Um();if(!t)throw new C(401,!1);return t}function Um(){return yi?.get(Rm)??null}function bE(e){e.get(vu,null)?.forEach(n=>n())}var $r=(()=>{class e{static{this.__NG_ELEMENT_ID__=_E}}return e})();function _E(e){return IE($e(),re(),(e&16)===16)}function IE(e,t,n){if(zs(e)&&!n){let r=wn(e.index,t);return new Gn(r,r)}else if(e.type&175){let r=t[Mt];return new Gn(r,t)}return null}var Zc=class{constructor(){}supports(t){return wm(t)}create(t){return new Kc(t)}},ME=(e,t)=>t,Kc=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||ME}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){let s=!r||n&&n.currentIndex<fp(r,i,o)?n:r,a=fp(s,i,o),l=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)i++;else{o||(o=[]);let c=a-i,u=l-i;if(c!=u){for(let g=0;g<c;g++){let f=g<o.length?o[g]:o[g]=0,m=f+g;u<=m&&m<c&&(o[g]=f+1)}let d=s.previousIndex;o[d]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!wm(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,i,o,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)o=t[a],s=this._trackByFn(a,o),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,o,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,o,s,a)),Object.is(n.item,o)||this._addIdentityChange(n,o)),n=n._next}else i=0,pD(t,a=>{s=this._trackByFn(i,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,i),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,i)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return t===null?o=this._itTail:(o=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,i),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new Qc(n,r),o,i)),t}_verifyReinsertion(t,n,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let i=t._prevRemoved,o=t._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let i=n===null?this._itHead:n._next;return t._next=i,t._prev=n,i===null?this._itTail=t:i._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Us),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Us),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Qc=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Yc=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Us=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Yc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,i=this.map.get(r);return i?i.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function fp(e,t,n){let r=e.previousIndex;if(r===null)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}function hp(){return new Lu([new Zc])}var Lu=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:hp})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||hp()),deps:[[e,new Jc,new js]]}}find(n){let r=this.factories.find(i=>i.supports(n));if(r!=null)return r;throw new C(901,!1)}}return e})();var Pm=ku(null,"core",[]),km=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(_(En))}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({})}}return e})();var Lm=new I("");function Hr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Ri(e,t){Kn("NgSignals");let n=Yf(e);return t?.equal&&(n[an].equal=t.equal),n}function Zt(e){let t=K(null);try{return e()}finally{K(t)}}function Vm(e){let t=mn(e);if(!t)return null;let n=new Rr(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var Wm=null;function Yn(){return Wm}function qm(e){Wm??=e}var wa=class{};var Ee=new I(""),Zu=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(AE),providedIn:"platform"})}}return e})(),Zm=new I(""),AE=(()=>{class e extends Zu{constructor(){super(),this._doc=w(Ee),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Yn().getBaseHref(this._doc)}onPopState(n){let r=Yn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Yn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,i){this._history.pushState(n,r,i)}replaceState(n,r,i){this._history.replaceState(n,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Ku(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function jm(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function Qt(e){return e&&e[0]!=="?"?"?"+e:e}var Xt=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(Qu),providedIn:"root"})}}return e})(),Km=new I(""),Qu=(()=>{class e extends Xt{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??w(Ee).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Ku(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Qt(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${r}${i}`:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(_(Zu),_(Km,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Qm=(()=>{class e extends Xt{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Ku(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(_(Zu),_(Km,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Gr=(()=>{class e{constructor(n){this._subject=new Ce,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=FE(jm(Bm(r))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Qt(r))}normalize(n){return e.stripTrailingSlash(xE(this._basePath,Bm(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",i=null){this._locationStrategy.pushState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Qt(r)),i)}replaceState(n,r="",i=null){this._locationStrategy.replaceState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Qt(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(i=>i(n,r))}subscribe(n,r,i){return this._subject.subscribe({next:n,error:r,complete:i})}static{this.normalizeQueryParams=Qt}static{this.joinWithSlash=Ku}static{this.stripTrailingSlash=jm}static{this.\u0275fac=function(r){return new(r||e)(_(Xt))}}static{this.\u0275prov=b({token:e,factory:()=>TE(),providedIn:"root"})}}return e})();function TE(){return new Gr(_(Xt))}function xE(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Bm(e){return e.replace(/\/index.html$/,"")}function FE(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Pe=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Pe||{}),ie=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(ie||{}),Ze=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ze||{}),bn={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function NE(e){return st(e)[ue.LocaleId]}function RE(e,t,n){let r=st(e),i=[r[ue.DayPeriodsFormat],r[ue.DayPeriodsStandalone]],o=lt(i,t);return lt(o,n)}function OE(e,t,n){let r=st(e),i=[r[ue.DaysFormat],r[ue.DaysStandalone]],o=lt(i,t);return lt(o,n)}function UE(e,t,n){let r=st(e),i=[r[ue.MonthsFormat],r[ue.MonthsStandalone]],o=lt(i,t);return lt(o,n)}function PE(e,t){let r=st(e)[ue.Eras];return lt(r,t)}function fa(e,t){let n=st(e);return lt(n[ue.DateFormat],t)}function ha(e,t){let n=st(e);return lt(n[ue.TimeFormat],t)}function pa(e,t){let r=st(e)[ue.DateTimeFormat];return lt(r,t)}function Ea(e,t){let n=st(e),r=n[ue.NumberSymbols][t];if(typeof r>"u"){if(t===bn.CurrencyDecimal)return n[ue.NumberSymbols][bn.Decimal];if(t===bn.CurrencyGroup)return n[ue.NumberSymbols][bn.Group]}return r}function Ym(e){if(!e[ue.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ue.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function kE(e){let t=st(e);return Ym(t),(t[ue.ExtraData][2]||[]).map(r=>typeof r=="string"?Vu(r):[Vu(r[0]),Vu(r[1])])}function LE(e,t,n){let r=st(e);Ym(r);let i=[r[ue.ExtraData][0],r[ue.ExtraData][1]],o=lt(i,t)||[];return lt(o,n)||[]}function lt(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Vu(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var VE=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ga={},jE=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Yt=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(Yt||{}),Y=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(Y||{}),Q=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(Q||{});function BE(e,t,n,r){let i=QE(e);t=Kt(n,t)||t;let s=[],a;for(;t;)if(a=jE.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let l=i.getTimezoneOffset();r&&(l=Jm(r,l),i=KE(i,r,!0));let c="";return s.forEach(u=>{let d=qE(u);c+=d?d(i,n,l):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Da(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Kt(e,t){let n=NE(e);if(ga[n]??={},ga[n][t])return ga[n][t];let r="";switch(t){case"shortDate":r=fa(e,Ze.Short);break;case"mediumDate":r=fa(e,Ze.Medium);break;case"longDate":r=fa(e,Ze.Long);break;case"fullDate":r=fa(e,Ze.Full);break;case"shortTime":r=ha(e,Ze.Short);break;case"mediumTime":r=ha(e,Ze.Medium);break;case"longTime":r=ha(e,Ze.Long);break;case"fullTime":r=ha(e,Ze.Full);break;case"short":let i=Kt(e,"shortTime"),o=Kt(e,"shortDate");r=ma(pa(e,Ze.Short),[i,o]);break;case"medium":let s=Kt(e,"mediumTime"),a=Kt(e,"mediumDate");r=ma(pa(e,Ze.Medium),[s,a]);break;case"long":let l=Kt(e,"longTime"),c=Kt(e,"longDate");r=ma(pa(e,Ze.Long),[l,c]);break;case"full":let u=Kt(e,"fullTime"),d=Kt(e,"fullDate");r=ma(pa(e,Ze.Full),[u,d]);break}return r&&(ga[n][t]=r),r}function ma(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function yt(e,t,n="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),o+s}function $E(e,t){return yt(e,3).substring(0,t)}function ye(e,t,n=0,r=!1,i=!1){return function(o,s){let a=HE(e,o);if((n>0||a>-n)&&(a+=n),e===Y.Hours)a===0&&n===-12&&(a=12);else if(e===Y.FractionalSeconds)return $E(a,t);let l=Ea(s,bn.MinusSign);return yt(a,t,l,r,i)}}function HE(e,t){switch(e){case Y.FullYear:return t.getFullYear();case Y.Month:return t.getMonth();case Y.Date:return t.getDate();case Y.Hours:return t.getHours();case Y.Minutes:return t.getMinutes();case Y.Seconds:return t.getSeconds();case Y.FractionalSeconds:return t.getMilliseconds();case Y.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function oe(e,t,n=Pe.Format,r=!1){return function(i,o){return zE(i,o,e,t,n,r)}}function zE(e,t,n,r,i,o){switch(n){case Q.Months:return UE(t,i,r)[e.getMonth()];case Q.Days:return OE(t,i,r)[e.getDay()];case Q.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(o){let c=kE(t),u=LE(t,i,r),d=c.findIndex(g=>{if(Array.isArray(g)){let[f,m]=g,y=s>=f.hours&&a>=f.minutes,D=s<m.hours||s===m.hours&&a<m.minutes;if(f.hours<m.hours){if(y&&D)return!0}else if(y||D)return!0}else if(g.hours===s&&g.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return RE(t,i,r)[s<12?0:1];case Q.Eras:return PE(t,r)[e.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function ya(e){return function(t,n,r){let i=-1*r,o=Ea(n,bn.MinusSign),s=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case Yt.Short:return(i>=0?"+":"")+yt(s,2,o)+yt(Math.abs(i%60),2,o);case Yt.ShortGMT:return"GMT"+(i>=0?"+":"")+yt(s,1,o);case Yt.Long:return"GMT"+(i>=0?"+":"")+yt(s,2,o)+":"+yt(Math.abs(i%60),2,o);case Yt.Extended:return r===0?"Z":(i>=0?"+":"")+yt(s,2,o)+":"+yt(Math.abs(i%60),2,o);default:throw new Error(`Unknown zone width "${e}"`)}}}var GE=0,Ca=4;function WE(e){let t=Da(e,GE,1).getDay();return Da(e,0,1+(t<=Ca?Ca:Ca+7)-t)}function Xm(e){let t=e.getDay(),n=t===0?-3:Ca-t;return Da(e.getFullYear(),e.getMonth(),e.getDate()+n)}function ju(e,t=!1){return function(n,r){let i;if(t){let o=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();i=1+Math.floor((s+o)/7)}else{let o=Xm(n),s=WE(o.getFullYear()),a=o.getTime()-s.getTime();i=1+Math.round(a/6048e5)}return yt(i,e,Ea(r,bn.MinusSign))}}function va(e,t=!1){return function(n,r){let o=Xm(n).getFullYear();return yt(o,e,Ea(r,bn.MinusSign),t)}}var Bu={};function qE(e){if(Bu[e])return Bu[e];let t;switch(e){case"G":case"GG":case"GGG":t=oe(Q.Eras,ie.Abbreviated);break;case"GGGG":t=oe(Q.Eras,ie.Wide);break;case"GGGGG":t=oe(Q.Eras,ie.Narrow);break;case"y":t=ye(Y.FullYear,1,0,!1,!0);break;case"yy":t=ye(Y.FullYear,2,0,!0,!0);break;case"yyy":t=ye(Y.FullYear,3,0,!1,!0);break;case"yyyy":t=ye(Y.FullYear,4,0,!1,!0);break;case"Y":t=va(1);break;case"YY":t=va(2,!0);break;case"YYY":t=va(3);break;case"YYYY":t=va(4);break;case"M":case"L":t=ye(Y.Month,1,1);break;case"MM":case"LL":t=ye(Y.Month,2,1);break;case"MMM":t=oe(Q.Months,ie.Abbreviated);break;case"MMMM":t=oe(Q.Months,ie.Wide);break;case"MMMMM":t=oe(Q.Months,ie.Narrow);break;case"LLL":t=oe(Q.Months,ie.Abbreviated,Pe.Standalone);break;case"LLLL":t=oe(Q.Months,ie.Wide,Pe.Standalone);break;case"LLLLL":t=oe(Q.Months,ie.Narrow,Pe.Standalone);break;case"w":t=ju(1);break;case"ww":t=ju(2);break;case"W":t=ju(1,!0);break;case"d":t=ye(Y.Date,1);break;case"dd":t=ye(Y.Date,2);break;case"c":case"cc":t=ye(Y.Day,1);break;case"ccc":t=oe(Q.Days,ie.Abbreviated,Pe.Standalone);break;case"cccc":t=oe(Q.Days,ie.Wide,Pe.Standalone);break;case"ccccc":t=oe(Q.Days,ie.Narrow,Pe.Standalone);break;case"cccccc":t=oe(Q.Days,ie.Short,Pe.Standalone);break;case"E":case"EE":case"EEE":t=oe(Q.Days,ie.Abbreviated);break;case"EEEE":t=oe(Q.Days,ie.Wide);break;case"EEEEE":t=oe(Q.Days,ie.Narrow);break;case"EEEEEE":t=oe(Q.Days,ie.Short);break;case"a":case"aa":case"aaa":t=oe(Q.DayPeriods,ie.Abbreviated);break;case"aaaa":t=oe(Q.DayPeriods,ie.Wide);break;case"aaaaa":t=oe(Q.DayPeriods,ie.Narrow);break;case"b":case"bb":case"bbb":t=oe(Q.DayPeriods,ie.Abbreviated,Pe.Standalone,!0);break;case"bbbb":t=oe(Q.DayPeriods,ie.Wide,Pe.Standalone,!0);break;case"bbbbb":t=oe(Q.DayPeriods,ie.Narrow,Pe.Standalone,!0);break;case"B":case"BB":case"BBB":t=oe(Q.DayPeriods,ie.Abbreviated,Pe.Format,!0);break;case"BBBB":t=oe(Q.DayPeriods,ie.Wide,Pe.Format,!0);break;case"BBBBB":t=oe(Q.DayPeriods,ie.Narrow,Pe.Format,!0);break;case"h":t=ye(Y.Hours,1,-12);break;case"hh":t=ye(Y.Hours,2,-12);break;case"H":t=ye(Y.Hours,1);break;case"HH":t=ye(Y.Hours,2);break;case"m":t=ye(Y.Minutes,1);break;case"mm":t=ye(Y.Minutes,2);break;case"s":t=ye(Y.Seconds,1);break;case"ss":t=ye(Y.Seconds,2);break;case"S":t=ye(Y.FractionalSeconds,1);break;case"SS":t=ye(Y.FractionalSeconds,2);break;case"SSS":t=ye(Y.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=ya(Yt.Short);break;case"ZZZZZ":t=ya(Yt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=ya(Yt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=ya(Yt.Long);break;default:return null}return Bu[e]=t,t}function Jm(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function ZE(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function KE(e,t,n){let r=n?-1:1,i=e.getTimezoneOffset(),o=Jm(t,i);return ZE(e,r*(o-i))}function QE(e){if($m(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,s=1]=e.split("-").map(a=>+a);return Da(i,o-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(VE))return YE(r)}let t=new Date(e);if(!$m(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function YE(e){let t=new Date(0),n=0,r=0,i=e[8]?t.setUTCFullYear:t.setFullYear,o=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(t,s,a,l,c),t}function $m(e){return e instanceof Date&&!isNaN(e.valueOf())}function ba(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[i,o]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(i.trim()===t)return decodeURIComponent(o)}return null}var $u=/\s+/,Hm=[],e0=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=Hm,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split($u):Hm}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split($u):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],i=n[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split($u).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static{this.\u0275fac=function(r){return new(r||e)($(zt),$(jr))}}static{this.\u0275dir=gt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var Hu=class{constructor(t,n,r,i){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Wr=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,i){this._viewContainer=n,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((i,o,s)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new Hu(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(o===null?void 0:o);else if(o!==null){let a=r.get(o);r.move(a,s),zm(a,i)}});for(let i=0,o=r.length;i<o;i++){let a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);zm(o,i)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)($(Br),$(ea),$(Lu))}}static{this.\u0275dir=gt({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function zm(e,t){e.context.$implicit=t.item}var _a=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new zu,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Gm("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Gm("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)($(Br),$(ea))}}static{this.\u0275dir=gt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),zu=class{constructor(){this.$implicit=null,this.ngIf=null}};function Gm(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Re(t)}'.`)}function XE(e,t){return new C(2100,!1)}var JE="mediumDate",e3=new I(""),t3=new I(""),t0=(()=>{class e{constructor(n,r,i){this.locale=n,this.defaultTimezone=r,this.defaultOptions=i}transform(n,r,i,o){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??JE,a=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return BE(n,s,o||this.locale,a)}catch(s){throw XE(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)($(da,16),$(e3,24),$(t3,24))}}static{this.\u0275pipe=xp({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var n0=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({})}}return e})(),Yu="browser",n3="server";function r3(e){return e===Yu}function Ia(e){return e===n3}var r0=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>r3(w(Tt))?new Gu(w(Ee),window):new Wu})}}return e})(),Gu=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=i3(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,i=n.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(r-o[0],i-o[1])}};function i3(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),i=r.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(t)||o.querySelector(`[name="${t}"]`);if(s)return s}i=r.nextNode()}}return null}var Wu=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},zr=class{};var Ui=class{},Sa=class{},Jt=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let i=n.slice(0,r),o=i.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let i=(t.op==="a"?this.headers.get(n):void 0)||[];i.push(...r),this.headers.set(n,i);break;case"d":let o=t.value;if(!o)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(o=>o.toString()),i=t.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(t,i)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Ju=class{encodeKey(t){return i0(t)}encodeValue(t){return i0(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function o3(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[t.decodeKey(i),""]:[t.decodeKey(i.slice(0,o)),t.decodeValue(i.slice(o+1))],l=n.get(s)||[];l.push(a),n.set(s,l)}),n}var s3=/%(\d[a-f0-9])/gi,a3={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function i0(e){return encodeURIComponent(e).replace(s3,(t,n)=>a3[n]??t)}function Ma(e){return`${e}`}var In=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new Ju,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=o3(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],i=Array.isArray(r)?r.map(Ma):[Ma(r)];this.map.set(n,i)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let i=t[r];Array.isArray(i)?i.forEach(o=>{n.push({param:r,value:o,op:"a"})}):n.push({param:r,value:i,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Ma(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],i=r.indexOf(Ma(t.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var ed=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function l3(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function o0(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function s0(e){return typeof Blob<"u"&&e instanceof Blob}function a0(e){return typeof FormData<"u"&&e instanceof FormData}function c3(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Oi=class e{constructor(t,n,r,i){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let o;if(l3(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new Jt,this.context??=new ed,!this.params)this.params=new In,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),l=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+l+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||o0(this.body)||s0(this.body)||a0(this.body)||c3(this.body)?this.body:this.body instanceof In?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||a0(this.body)?null:s0(this.body)?this.body.type||null:o0(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof In?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,i=t.responseType||this.responseType,o=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,l=t.reportProgress??this.reportProgress,c=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(c=Object.keys(t.setHeaders).reduce((g,f)=>g.set(f,t.setHeaders[f]),c)),t.setParams&&(u=Object.keys(t.setParams).reduce((g,f)=>g.set(f,t.setParams[f]),u)),new e(n,r,s,{params:u,headers:c,context:d,reportProgress:l,responseType:i,withCredentials:a,transferCache:o})}},Mn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Mn||{}),Pi=class{constructor(t,n=200,r="OK"){this.headers=t.headers||new Jt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Aa=class e extends Pi{constructor(t={}){super(t),this.type=Mn.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},ki=class e extends Pi{constructor(t={}){super(t),this.type=Mn.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},_n=class extends Pi{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},f0=200,u3=204;function Xu(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var en=(()=>{class e{constructor(n){this.handler=n}request(n,r,i={}){let o;if(n instanceof Oi)o=n;else{let l;i.headers instanceof Jt?l=i.headers:l=new Jt(i.headers);let c;i.params&&(i.params instanceof In?c=i.params:c=new In({fromObject:i.params})),o=new Oi(n,r,i.body!==void 0?i.body:null,{headers:l,context:i.context,params:c,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=F(o).pipe(kt(l=>this.handler.handle(l)));if(n instanceof Oi||i.observe==="events")return s;let a=s.pipe(Le(l=>l instanceof ki));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(P(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(P(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(P(l=>{if(l.body!==null&&typeof l.body!="string")throw new Error("Response is not a string.");return l.body}));case"json":default:return a.pipe(P(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new In().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,i={}){return this.request("PATCH",n,Xu(i,r))}post(n,r,i={}){return this.request("POST",n,Xu(i,r))}put(n,r,i={}){return this.request("PUT",n,Xu(i,r))}static{this.\u0275fac=function(r){return new(r||e)(_(Ui))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),d3=/^\)\]\}',?\n/,f3="X-Request-URL";function l0(e){if(e.url)return e.url;let t=f3.toLocaleLowerCase();return e.headers.get(t)}var h3=(()=>{class e{constructor(){this.fetchImpl=w(td,{optional:!0})?.fetch??fetch.bind(globalThis),this.ngZone=w(X)}handle(n){return new z(r=>{let i=new AbortController;return this.doRequest(n,i.signal,r).then(nd,o=>r.error(new _n({error:o}))),()=>i.abort()})}doRequest(n,r,i){return _o(this,null,function*(){let o=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,E({signal:r},o)));p3(f),i.next({type:Mn.Sent}),s=yield f}catch(f){i.error(new _n({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new Jt(s.headers),l=s.statusText,c=l0(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&i.next(new Aa({headers:a,status:u,statusText:l,url:c})),s.body){let f=s.headers.get("content-length"),m=[],y=s.body.getReader(),D=0,S,H,A=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>_o(this,null,function*(){for(;;){let{done:ce,value:te}=yield y.read();if(ce)break;if(m.push(te),D+=te.length,n.reportProgress){H=n.responseType==="text"?(H??"")+(S??=new TextDecoder).decode(te,{stream:!0}):void 0;let se=()=>i.next({type:Mn.DownloadProgress,total:f?+f:void 0,loaded:D,partialText:H});A?A.run(se):se()}}}));let W=this.concatChunks(m,D);try{let ce=s.headers.get("Content-Type")??"";d=this.parseBody(n,W,ce)}catch(ce){i.error(new _n({error:ce,headers:new Jt(s.headers),status:s.status,statusText:s.statusText,url:l0(s)??n.urlWithParams}));return}}u===0&&(u=d?f0:0),u>=200&&u<300?(i.next(new ki({body:d,headers:a,status:u,statusText:l,url:c})),i.complete()):i.error(new _n({error:d,headers:a,status:u,statusText:l,url:c}))})}parseBody(n,r,i){switch(n.responseType){case"json":let o=new TextDecoder().decode(r).replace(d3,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},i=n.withCredentials?"include":void 0;if(n.headers.forEach((o,s)=>r[o]=s.join(",")),n.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!n.headers.has("Content-Type")){let o=n.detectContentTypeHeader();o!==null&&(r["Content-Type"]=o)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:i}}concatChunks(n,r){let i=new Uint8Array(r),o=0;for(let s of n)i.set(s,o),o+=s.length;return i}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),td=class{};function nd(){}function p3(e){e.then(nd,nd)}function h0(e,t){return t(e)}function g3(e,t){return(n,r)=>t.intercept(n,{handle:i=>e(i,r)})}function m3(e,t,n){return(r,i)=>qe(n,()=>t(r,o=>e(o,i)))}var y3=new I(""),rd=new I(""),v3=new I(""),p0=new I("",{providedIn:"root",factory:()=>!0});function C3(){let e=null;return(t,n)=>{e===null&&(e=(w(y3,{optional:!0})??[]).reduceRight(g3,h0));let r=w(Ht);if(w(p0)){let o=r.add();return e(t,n).pipe(dn(()=>r.remove(o)))}else return e(t,n)}}var c0=(()=>{class e extends Ui{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=w(Ht),this.contributeToStability=w(p0)}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(rd),...this.injector.get(v3,[])]));this.chain=r.reduceRight((i,o)=>m3(i,o,this.injector),h0)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(dn(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(_(Sa),_(Oe))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();var w3=/^\)\]\}',?\n/;function D3(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var u0=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new C(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ae(r.\u0275loadImpl()):F(null)).pipe(Ve(()=>new z(o=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((y,D)=>s.setRequestHeader(y,D.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let y=n.detectContentTypeHeader();y!==null&&s.setRequestHeader("Content-Type",y)}if(n.responseType){let y=n.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=n.serializeBody(),l=null,c=()=>{if(l!==null)return l;let y=s.statusText||"OK",D=new Jt(s.getAllResponseHeaders()),S=D3(s)||n.url;return l=new Aa({headers:D,status:s.status,statusText:y,url:S}),l},u=()=>{let{headers:y,status:D,statusText:S,url:H}=c(),A=null;D!==u3&&(A=typeof s.response>"u"?s.responseText:s.response),D===0&&(D=A?f0:0);let W=D>=200&&D<300;if(n.responseType==="json"&&typeof A=="string"){let ce=A;A=A.replace(w3,"");try{A=A!==""?JSON.parse(A):null}catch(te){A=ce,W&&(W=!1,A={error:te,text:A})}}W?(o.next(new ki({body:A,headers:y,status:D,statusText:S,url:H||void 0})),o.complete()):o.error(new _n({error:A,headers:y,status:D,statusText:S,url:H||void 0}))},d=y=>{let{url:D}=c(),S=new _n({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:D||void 0});o.error(S)},g=!1,f=y=>{g||(o.next(c()),g=!0);let D={type:Mn.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(D.total=y.total),n.responseType==="text"&&s.responseText&&(D.partialText=s.responseText),o.next(D)},m=y=>{let D={type:Mn.UploadProgress,loaded:y.loaded};y.lengthComputable&&(D.total=y.total),o.next(D)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",m)),s.send(a),o.next({type:Mn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",m)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(_(zr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),g0=new I(""),E3="XSRF-TOKEN",b3=new I("",{providedIn:"root",factory:()=>E3}),_3="X-XSRF-TOKEN",I3=new I("",{providedIn:"root",factory:()=>_3}),Ta=class{},M3=(()=>{class e{constructor(n,r,i){this.doc=n,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=ba(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(_(Ee),_(Tt),_(b3))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function S3(e,t){let n=e.url.toLowerCase();if(!w(g0)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=w(Ta).getToken(),i=w(I3);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),t(e)}var m0=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(m0||{});function A3(e,t){return{\u0275kind:e,\u0275providers:t}}function T3(...e){let t=[en,u0,c0,{provide:Ui,useExisting:c0},{provide:Sa,useFactory:()=>w(h3,{optional:!0})??w(u0)},{provide:rd,useValue:S3,multi:!0},{provide:g0,useValue:!0},{provide:Ta,useClass:M3}];for(let n of e)t.push(...n.\u0275providers);return Bs(t)}var d0=new I("");function x3(){return A3(m0.LegacyInterceptors,[{provide:d0,useFactory:C3},{provide:rd,useExisting:d0,multi:!0}])}var y0=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({providers:[T3(x3())]})}}return e})();var sd=class extends wa{constructor(){super(...arguments),this.supportsDOMEvents=!0}},ad=class e extends sd{static makeCurrent(){qm(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=F3();return n==null?null:N3(n)}resetBaseElement(){Li=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return ba(document.cookie,t)}},Li=null;function F3(){return Li=Li||document.querySelector("base"),Li?Li.getAttribute("href"):null}function N3(e){return new URL(e,document.baseURI).pathname}var ld=class{addToWindow(t){Fe.getAngularTestability=(r,i=!0)=>{let o=t.findTestabilityInTree(r,i);if(o==null)throw new C(5103,!1);return o},Fe.getAllAngularTestabilities=()=>t.getAllTestabilities(),Fe.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let i=Fe.getAllAngularTestabilities(),o=i.length,s=function(){o--,o==0&&r()};i.forEach(a=>{a.whenStable(s)})};Fe.frameworkStabilizers||(Fe.frameworkStabilizers=[]),Fe.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let i=t.getTestability(n);return i??(r?Yn().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},R3=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Fa=new I(""),w0=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,i){return this._findPluginFor(r).addEventListener(n,r,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(o=>o.supports(n)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(_(Fa),_(X))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Vi=class{constructor(t){this._doc=t}},id="ng-app-id",D0=(()=>{class e{constructor(n,r,i,o={}){this.doc=n,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Ia(o),this.resetHostNodes()}addStyles(n){for(let r of n)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(n){for(let r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){let r=this.styleRef;r.get(n)?.elements?.forEach(i=>i.remove()),r.delete(n)}collectServerRenderedStyles(){let n=this.doc.head?.querySelectorAll(`style[${id}="${this.appId}"]`);if(n?.length){let r=new Map;return n.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(n,r){let i=this.styleRef;if(i.has(n)){let o=i.get(n);return o.usage+=r,o.usage}return i.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){let i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===n)return i.delete(r),o.removeAttribute(id),o;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(id,this.appId),n.appendChild(s),s}}addStyleToHost(n,r){let i=this.getStyleElement(n,r),o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(_(Ee),_(Zs),_(wu,8),_(Tt))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),od={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},ud=/%COMP%/g,E0="%COMP%",O3=`_nghost-${E0}`,U3=`_ngcontent-${E0}`,P3=!0,k3=new I("",{providedIn:"root",factory:()=>P3});function L3(e){return U3.replace(ud,e)}function V3(e){return O3.replace(ud,e)}function b0(e,t){return t.map(n=>n.replace(ud,e))}var Na=(()=>{class e{constructor(n,r,i,o,s,a,l,c=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=Ia(a),this.defaultRenderer=new ji(n,s,l,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===_t.ShadowDom&&(r=Z(E({},r),{encapsulation:_t.Emulated}));let i=this.getOrCreateRenderer(n,r);return i instanceof Ra?i.applyToHost(n):i instanceof Bi&&i.applyStyles(),i}getOrCreateRenderer(n,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case _t.Emulated:o=new Ra(l,c,r,this.appId,u,s,a,d);break;case _t.ShadowDom:return new cd(l,c,n,r,s,a,this.nonce,d);default:o=new Bi(l,c,r,u,s,a,d);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(_(w0),_(D0),_(Zs),_(k3),_(Ee),_(Tt),_(X),_(wu))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),ji=class{constructor(t,n,r,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(od[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(v0(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(v0(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new C(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,i){if(i){n=i+":"+n;let o=od[i];o?t.setAttributeNS(o,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let i=od[r];i?t.removeAttributeNS(i,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,i){i&(jt.DashCase|jt.Important)?t.style.setProperty(n,r,i&jt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&jt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=Yn().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function v0(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var cd=class extends ji{constructor(t,n,r,i,o,s,a,l){super(t,o,s,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let c=b0(i.id,i.styles);for(let u of c){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Bi=class extends ji{constructor(t,n,r,i,o,s,a,l){super(t,o,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=i,this.styles=l?b0(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Ra=class extends Bi{constructor(t,n,r,i,o,s,a,l){let c=i+"-"+r.id;super(t,n,r,o,s,a,l,c),this.contentAttr=L3(c),this.hostAttr=V3(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},j3=(()=>{class e extends Vi{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,i){return n.addEventListener(r,i,!1),()=>this.removeEventListener(n,r,i)}removeEventListener(n,r,i){return n.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||e)(_(Ee))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),B3=(()=>{class e extends Vi{constructor(n){super(n),this.delegate=w(Lm,{optional:!0})}supports(n){return this.delegate?this.delegate.supports(n):!1}addEventListener(n,r,i){return this.delegate.addEventListener(n,r,i)}removeEventListener(n,r,i){return this.delegate.removeEventListener(n,r,i)}static{this.\u0275fac=function(r){return new(r||e)(_(Ee))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),C0=["alt","control","meta","shift"],$3={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},H3={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},z3=(()=>{class e extends Vi{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,i){let o=e.parseEventName(r),s=e.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Yn().onAndCancel(n,o.domEventName,s))}static parseEventName(n){let r=n.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),C0.forEach(c=>{let u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=o,r.length!=0||o.length===0)return null;let l={};return l.domEventName=i,l.fullKey=s,l}static matchEventFullKeyCode(n,r){let i=$3[n.key]||n.key,o="";return r.indexOf("code.")>-1&&(i=n.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),C0.forEach(s=>{if(s!==i){let a=H3[s];a(n)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(n,r,i){return o=>{e.matchEventFullKeyCode(o,n)&&i.runGuarded(()=>r(o))}}static _normalizeKey(n){return n==="esc"?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(_(Ee))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function G3(){ad.makeCurrent()}function W3(){return new Vt}function q3(){return Og(document),document}var Z3=[{provide:Tt,useValue:Yu},{provide:vu,useValue:G3,multi:!0},{provide:Ee,useFactory:q3,deps:[]}],_0=ku(Pm,"browser",Z3),K3=new I(""),Q3=[{provide:Ni,useClass:ld,deps:[]},{provide:Uu,useClass:sa,deps:[X,aa,Ni]},{provide:sa,useClass:sa,deps:[X,aa,Ni]}],Y3=[{provide:$s,useValue:"root"},{provide:Vt,useFactory:W3,deps:[]},{provide:Fa,useClass:j3,multi:!0,deps:[Ee,X,Tt]},{provide:Fa,useClass:z3,multi:!0,deps:[Ee]},{provide:Fa,useClass:B3,multi:!0},Na,D0,w0,{provide:vn,useExisting:Na},{provide:zr,useClass:R3,deps:[]},[]],Oa=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:Zs,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(_(K3,12))}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({providers:[...Y3,...Q3],imports:[n0,km]})}}return e})();var I0=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(_(Ee))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var O=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(O||{}),xt="*";function Ua(e,t){return{type:O.Trigger,name:e,definitions:t,options:{}}}function Pa(e,t=null){return{type:O.Animate,styles:t,timings:e}}function S0(e,t=null){return{type:O.Sequence,steps:e,options:t}}function Ft(e){return{type:O.Style,styles:e,offset:null}}function dd(e,t,n){return{type:O.State,name:e,styles:t,options:n}}function ka(e,t,n=null){return{type:O.Transition,expr:e,animation:t,options:n}}function A0(e,t,n=null){return{type:O.Query,selector:e,animation:t,options:n}}function T0(e,t){return{type:O.Stagger,timings:e,animation:t}}var Sn=class{constructor(t=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},$i=class{constructor(t){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=t;let n=0,r=0,i=0,o=this.players.length;o==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==o&&this._onFinish()}),s.onDestroy(()=>{++r==o&&this._onDestroy()}),s.onStart(()=>{++i==o&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let i=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(i)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},La="!";function x0(e){return new C(3e3,!1)}function X3(){return new C(3100,!1)}function J3(){return new C(3101,!1)}function eb(e){return new C(3001,!1)}function tb(e){return new C(3003,!1)}function nb(e){return new C(3004,!1)}function rb(e,t){return new C(3005,!1)}function ib(){return new C(3006,!1)}function ob(){return new C(3007,!1)}function sb(e,t){return new C(3008,!1)}function ab(e){return new C(3002,!1)}function lb(e,t,n,r,i){return new C(3010,!1)}function cb(){return new C(3011,!1)}function ub(){return new C(3012,!1)}function db(){return new C(3200,!1)}function fb(){return new C(3202,!1)}function hb(){return new C(3013,!1)}function pb(e){return new C(3014,!1)}function gb(e){return new C(3015,!1)}function mb(e){return new C(3016,!1)}function yb(e,t){return new C(3404,!1)}function vb(e){return new C(3502,!1)}function Cb(e){return new C(3503,!1)}function wb(){return new C(3300,!1)}function Db(e){return new C(3504,!1)}function Eb(e){return new C(3301,!1)}function bb(e,t){return new C(3302,!1)}function _b(e){return new C(3303,!1)}function Ib(e,t){return new C(3400,!1)}function Mb(e){return new C(3401,!1)}function Sb(e){return new C(3402,!1)}function Ab(e,t){return new C(3505,!1)}function An(e){switch(e.length){case 0:return new Sn;case 1:return e[0];default:return new $i(e)}}function z0(e,t,n=new Map,r=new Map){let i=[],o=[],s=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),u=c==s,d=u&&a||new Map;l.forEach((g,f)=>{let m=f,y=g;if(f!=="offset")switch(m=e.normalizePropertyName(m,i),y){case La:y=n.get(f);break;case xt:y=r.get(f);break;default:y=e.normalizeStyleValue(f,m,y,i);break}d.set(m,y)}),u||o.push(d),a=d,s=c}),i.length)throw vb(i);return o}function Od(e,t,n,r){switch(t){case"start":e.onStart(()=>r(n&&fd(n,"start",e)));break;case"done":e.onDone(()=>r(n&&fd(n,"done",e)));break;case"destroy":e.onDestroy(()=>r(n&&fd(n,"destroy",e)));break}}function fd(e,t,n){let r=n.totalTime,i=!!n.disabled,o=Ud(e.element,e.triggerName,e.fromState,e.toState,t||e.phaseName,r??e.totalTime,i),s=e._data;return s!=null&&(o._data=s),o}function Ud(e,t,n,r,i="",o=0,s){return{element:e,triggerName:t,fromState:n,toState:r,phaseName:i,totalTime:o,disabled:!!s}}function Qe(e,t,n){let r=e.get(t);return r||e.set(t,r=n),r}function F0(e){let t=e.indexOf(":"),n=e.substring(1,t),r=e.slice(t+1);return[n,r]}var Tb=typeof document>"u"?null:document.documentElement;function Pd(e){let t=e.parentNode||e.host||null;return t===Tb?null:t}function xb(e){return e.substring(1,6)=="ebkit"}var Xn=null,N0=!1;function Fb(e){Xn||(Xn=Nb()||{},N0=Xn.style?"WebkitAppearance"in Xn.style:!1);let t=!0;return Xn.style&&!xb(e)&&(t=e in Xn.style,!t&&N0&&(t="Webkit"+e.charAt(0).toUpperCase()+e.slice(1)in Xn.style)),t}function Nb(){return typeof document<"u"?document.body:null}function G0(e,t){for(;t;){if(t===e)return!0;t=Pd(t)}return!1}function W0(e,t,n){if(n)return Array.from(e.querySelectorAll(t));let r=e.querySelector(t);return r?[r]:[]}var kd=(()=>{class e{validateStyleProperty(n){return Fb(n)}containsElement(n,r){return G0(n,r)}getParentElement(n){return Pd(n)}query(n,r,i){return W0(n,r,i)}computeStyle(n,r,i){return i||""}animate(n,r,i,o,s,a=[],l){return new Sn(i,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),tr=class{static{this.NOOP=new kd}},nr=class{};var Rb=1e3,q0="{{",Ob="}}",Z0="ng-enter",vd="ng-leave",Va="ng-trigger",za=".ng-trigger",R0="ng-animating",Cd=".ng-animating";function tn(e){if(typeof e=="number")return e;let t=e.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:wd(parseFloat(t[1]),t[2])}function wd(e,t){switch(t){case"s":return e*Rb;default:return e}}function Ga(e,t,n){return e.hasOwnProperty("duration")?e:Ub(e,t,n)}function Ub(e,t,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,o=0,s="";if(typeof e=="string"){let a=e.match(r);if(a===null)return t.push(x0(e)),{duration:0,delay:0,easing:""};i=wd(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(o=wd(parseFloat(l),a[4]));let c=a[5];c&&(s=c)}else i=e;if(!n){let a=!1,l=t.length;i<0&&(t.push(X3()),a=!0),o<0&&(t.push(J3()),a=!0),a&&t.splice(l,0,x0(e))}return{duration:i,delay:o,easing:s}}function Pb(e){return e.length?e[0]instanceof Map?e:e.map(t=>new Map(Object.entries(t))):[]}function Nt(e,t,n){t.forEach((r,i)=>{let o=Ld(i);n&&!n.has(i)&&n.set(i,e.style[o]),e.style[o]=r})}function er(e,t){t.forEach((n,r)=>{let i=Ld(r);e.style[i]=""})}function Hi(e){return Array.isArray(e)?e.length==1?e[0]:S0(e):e}function kb(e,t,n){let r=t.params||{},i=K0(e);i.length&&i.forEach(o=>{r.hasOwnProperty(o)||n.push(eb(o))})}var Dd=new RegExp(`${q0}\\s*(.+?)\\s*${Ob}`,"g");function K0(e){let t=[];if(typeof e=="string"){let n;for(;n=Dd.exec(e);)t.push(n[1]);Dd.lastIndex=0}return t}function Gi(e,t,n){let r=`${e}`,i=r.replace(Dd,(o,s)=>{let a=t[s];return a==null&&(n.push(tb(s)),a=""),a.toString()});return i==r?e:i}var Lb=/-+([a-z0-9])/g;function Ld(e){return e.replace(Lb,(...t)=>t[1].toUpperCase())}function Vb(e,t){return e===0||t===0}function jb(e,t,n){if(n.size&&t.length){let r=t[0],i=[];if(n.forEach((o,s)=>{r.has(s)||i.push(s),r.set(s,o)}),i.length)for(let o=1;o<t.length;o++){let s=t[o];i.forEach(a=>s.set(a,Vd(e,a)))}}return t}function Ke(e,t,n){switch(t.type){case O.Trigger:return e.visitTrigger(t,n);case O.State:return e.visitState(t,n);case O.Transition:return e.visitTransition(t,n);case O.Sequence:return e.visitSequence(t,n);case O.Group:return e.visitGroup(t,n);case O.Animate:return e.visitAnimate(t,n);case O.Keyframes:return e.visitKeyframes(t,n);case O.Style:return e.visitStyle(t,n);case O.Reference:return e.visitReference(t,n);case O.AnimateChild:return e.visitAnimateChild(t,n);case O.AnimateRef:return e.visitAnimateRef(t,n);case O.Query:return e.visitQuery(t,n);case O.Stagger:return e.visitStagger(t,n);default:throw nb(t.type)}}function Vd(e,t){return window.getComputedStyle(e)[t]}var Bb=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Wa=class extends nr{normalizePropertyName(t,n){return Ld(t)}normalizeStyleValue(t,n,r,i){let o="",s=r.toString().trim();if(Bb.has(n)&&r!==0&&r!=="0")if(typeof r=="number")o="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&i.push(rb(t,r))}return s+o}};var qa="*";function $b(e,t){let n=[];return typeof e=="string"?e.split(/\s*,\s*/).forEach(r=>Hb(r,n,t)):n.push(e),n}function Hb(e,t,n){if(e[0]==":"){let l=zb(e,n);if(typeof l=="function"){t.push(l);return}e=l}let r=e.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(gb(e)),t;let i=r[1],o=r[2],s=r[3];t.push(O0(i,s));let a=i==qa&&s==qa;o[0]=="<"&&!a&&t.push(O0(s,i))}function zb(e,t){switch(e){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return t.push(mb(e)),"* => *"}}var ja=new Set(["true","1"]),Ba=new Set(["false","0"]);function O0(e,t){let n=ja.has(e)||Ba.has(e),r=ja.has(t)||Ba.has(t);return(i,o)=>{let s=e==qa||e==i,a=t==qa||t==o;return!s&&n&&typeof i=="boolean"&&(s=i?ja.has(e):Ba.has(e)),!a&&r&&typeof o=="boolean"&&(a=o?ja.has(t):Ba.has(t)),s&&a}}var Q0=":self",Gb=new RegExp(`s*${Q0}s*,?`,"g");function Y0(e,t,n,r){return new Ed(e).build(t,n,r)}var U0="",Ed=class{constructor(t){this._driver=t}build(t,n,r){let i=new bd(n);return this._resetContextStyleTimingState(i),Ke(this,Hi(t),i)}_resetContextStyleTimingState(t){t.currentQuerySelector=U0,t.collectedStyles=new Map,t.collectedStyles.set(U0,new Map),t.currentTime=0}visitTrigger(t,n){let r=n.queryCount=0,i=n.depCount=0,o=[],s=[];return t.name.charAt(0)=="@"&&n.errors.push(ib()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==O.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,o.push(this.visitState(l,n))}),l.name=c}else if(a.type==O.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,i+=l.depCount,s.push(l)}else n.errors.push(ob())}),{type:O.Trigger,name:t.name,states:o,transitions:s,queryCount:r,depCount:i,options:null}}visitState(t,n){let r=this.visitStyle(t.styles,n),i=t.options&&t.options.params||null;if(r.containsDynamicStyles){let o=new Set,s=i||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{K0(l).forEach(c=>{s.hasOwnProperty(c)||o.add(c)})})}),o.size&&n.errors.push(sb(t.name,[...o.values()]))}return{type:O.State,name:t.name,style:r,options:i?{params:i}:null}}visitTransition(t,n){n.queryCount=0,n.depCount=0;let r=Ke(this,Hi(t.animation),n),i=$b(t.expr,n.errors);return{type:O.Transition,matchers:i,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:Jn(t.options)}}visitSequence(t,n){return{type:O.Sequence,steps:t.steps.map(r=>Ke(this,r,n)),options:Jn(t.options)}}visitGroup(t,n){let r=n.currentTime,i=0,o=t.steps.map(s=>{n.currentTime=r;let a=Ke(this,s,n);return i=Math.max(i,n.currentTime),a});return n.currentTime=i,{type:O.Group,steps:o,options:Jn(t.options)}}visitAnimate(t,n){let r=Kb(t.timings,n.errors);n.currentAnimateTimings=r;let i,o=t.styles?t.styles:Ft({});if(o.type==O.Keyframes)i=this.visitKeyframes(o,n);else{let s=t.styles,a=!1;if(!s){a=!0;let c={};r.easing&&(c.easing=r.easing),s=Ft(c)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,i=l}return n.currentAnimateTimings=null,{type:O.Animate,timings:r,style:i,options:null}}visitStyle(t,n){let r=this._makeStyleAst(t,n);return this._validateStyleAst(r,n),r}_makeStyleAst(t,n){let r=[],i=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of i)typeof a=="string"?a===xt?r.push(a):n.errors.push(ab(a)):r.push(new Map(Object.entries(a)));let o=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!o)){for(let l of a.values())if(l.toString().indexOf(q0)>=0){o=!0;break}}}),{type:O.Style,styles:r,easing:s,offset:t.offset,containsDynamicStyles:o,options:null}}_validateStyleAst(t,n){let r=n.currentAnimateTimings,i=n.currentTime,o=n.currentTime;r&&o>0&&(o-=r.duration+r.delay),t.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let c=n.collectedStyles.get(n.currentQuerySelector),u=c.get(l),d=!0;u&&(o!=i&&o>=u.startTime&&i<=u.endTime&&(n.errors.push(lb(l,u.startTime,u.endTime,o,i)),d=!1),o=u.startTime),d&&c.set(l,{startTime:o,endTime:i}),n.options&&kb(a,n.options,n.errors)})})}visitKeyframes(t,n){let r={type:O.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(cb()),r;let i=1,o=0,s=[],a=!1,l=!1,c=0,u=t.steps.map(S=>{let H=this._makeStyleAst(S,n),A=H.offset!=null?H.offset:Zb(H.styles),W=0;return A!=null&&(o++,W=H.offset=A),l=l||W<0||W>1,a=a||W<c,c=W,s.push(W),H});l&&n.errors.push(ub()),a&&n.errors.push(db());let d=t.steps.length,g=0;o>0&&o<d?n.errors.push(fb()):o==0&&(g=i/(d-1));let f=d-1,m=n.currentTime,y=n.currentAnimateTimings,D=y.duration;return u.forEach((S,H)=>{let A=g>0?H==f?1:g*H:s[H],W=A*D;n.currentTime=m+y.delay+W,y.duration=W,this._validateStyleAst(S,n),S.offset=A,r.styles.push(S)}),r}visitReference(t,n){return{type:O.Reference,animation:Ke(this,Hi(t.animation),n),options:Jn(t.options)}}visitAnimateChild(t,n){return n.depCount++,{type:O.AnimateChild,options:Jn(t.options)}}visitAnimateRef(t,n){return{type:O.AnimateRef,animation:this.visitReference(t.animation,n),options:Jn(t.options)}}visitQuery(t,n){let r=n.currentQuerySelector,i=t.options||{};n.queryCount++,n.currentQuery=t;let[o,s]=Wb(t.selector);n.currentQuerySelector=r.length?r+" "+o:o,Qe(n.collectedStyles,n.currentQuerySelector,new Map);let a=Ke(this,Hi(t.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:O.Query,selector:o,limit:i.limit||0,optional:!!i.optional,includeSelf:s,animation:a,originalSelector:t.selector,options:Jn(t.options)}}visitStagger(t,n){n.currentQuery||n.errors.push(hb());let r=t.timings==="full"?{duration:0,delay:0,easing:"full"}:Ga(t.timings,n.errors,!0);return{type:O.Stagger,animation:Ke(this,Hi(t.animation),n),timings:r,options:null}}};function Wb(e){let t=!!e.split(/\s*,\s*/).find(n=>n==Q0);return t&&(e=e.replace(Gb,"")),e=e.replace(/@\*/g,za).replace(/@\w+/g,n=>za+"-"+n.slice(1)).replace(/:animating/g,Cd),[e,t]}function qb(e){return e?E({},e):null}var bd=class{constructor(t){this.errors=t,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function Zb(e){if(typeof e=="string")return null;let t=null;if(Array.isArray(e))e.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;t=parseFloat(r.get("offset")),r.delete("offset")}});else if(e instanceof Map&&e.has("offset")){let n=e;t=parseFloat(n.get("offset")),n.delete("offset")}return t}function Kb(e,t){if(e.hasOwnProperty("duration"))return e;if(typeof e=="number"){let o=Ga(e,t).duration;return hd(o,0,"")}let n=e;if(n.split(/\s+/).some(o=>o.charAt(0)=="{"&&o.charAt(1)=="{")){let o=hd(0,0,"");return o.dynamic=!0,o.strValue=n,o}let i=Ga(n,t);return hd(i.duration,i.delay,i.easing)}function Jn(e){return e?(e=E({},e),e.params&&(e.params=qb(e.params))):e={},e}function hd(e,t,n){return{duration:e,delay:t,easing:n}}function jd(e,t,n,r,i,o,s=null,a=!1){return{type:1,element:e,keyframes:t,preStyleProps:n,postStyleProps:r,duration:i,delay:o,totalTime:i+o,easing:s,subTimeline:a}}var Wi=class{constructor(){this._map=new Map}get(t){return this._map.get(t)||[]}append(t,n){let r=this._map.get(t);r||this._map.set(t,r=[]),r.push(...n)}has(t){return this._map.has(t)}clear(){this._map.clear()}},Qb=1,Yb=":enter",Xb=new RegExp(Yb,"g"),Jb=":leave",e_=new RegExp(Jb,"g");function X0(e,t,n,r,i,o=new Map,s=new Map,a,l,c=[]){return new _d().buildKeyframes(e,t,n,r,i,o,s,a,l,c)}var _d=class{buildKeyframes(t,n,r,i,o,s,a,l,c,u=[]){c=c||new Wi;let d=new Id(t,n,c,i,o,u,[]);d.options=l;let g=l.delay?tn(l.delay):0;d.currentTimeline.delayNextStep(g),d.currentTimeline.setStyles([s],null,d.errors,l),Ke(this,r,d);let f=d.timelines.filter(m=>m.containsAnimation());if(f.length&&a.size){let m;for(let y=f.length-1;y>=0;y--){let D=f[y];if(D.element===n){m=D;break}}m&&!m.allowOnlyTimelineStyles()&&m.setStyles([a],null,d.errors,l)}return f.length?f.map(m=>m.buildKeyframes()):[jd(n,[],[],[],0,g,"",!1)]}visitTrigger(t,n){}visitState(t,n){}visitTransition(t,n){}visitAnimateChild(t,n){let r=n.subInstructions.get(n.element);if(r){let i=n.createSubContext(t.options),o=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,i,i.options);o!=s&&n.transformIntoNewTimeline(s)}n.previousNode=t}visitAnimateRef(t,n){let r=n.createSubContext(t.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],n,r),this.visitReference(t.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=t}_applyAnimationRefDelays(t,n,r){for(let i of t){let o=i?.delay;if(o){let s=typeof o=="number"?o:tn(Gi(o,i?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(t,n,r){let o=n.currentTimeline.currentTime,s=r.duration!=null?tn(r.duration):null,a=r.delay!=null?tn(r.delay):null;return s!==0&&t.forEach(l=>{let c=n.appendInstructionToTimeline(l,s,a);o=Math.max(o,c.duration+c.delay)}),o}visitReference(t,n){n.updateOptions(t.options,!0),Ke(this,t.animation,n),n.previousNode=t}visitSequence(t,n){let r=n.subContextCount,i=n,o=t.options;if(o&&(o.params||o.delay)&&(i=n.createSubContext(o),i.transformIntoNewTimeline(),o.delay!=null)){i.previousNode.type==O.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=Za);let s=tn(o.delay);i.delayNextStep(s)}t.steps.length&&(t.steps.forEach(s=>Ke(this,s,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>r&&i.transformIntoNewTimeline()),n.previousNode=t}visitGroup(t,n){let r=[],i=n.currentTimeline.currentTime,o=t.options&&t.options.delay?tn(t.options.delay):0;t.steps.forEach(s=>{let a=n.createSubContext(t.options);o&&a.delayNextStep(o),Ke(this,s,a),i=Math.max(i,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(i),n.previousNode=t}_visitTiming(t,n){if(t.dynamic){let r=t.strValue,i=n.params?Gi(r,n.params,n.errors):r;return Ga(i,n.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,n){let r=n.currentAnimateTimings=this._visitTiming(t.timings,n),i=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),i.snapshotCurrentStyles());let o=t.style;o.type==O.Keyframes?this.visitKeyframes(o,n):(n.incrementTime(r.duration),this.visitStyle(o,n),i.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=t}visitStyle(t,n){let r=n.currentTimeline,i=n.currentAnimateTimings;!i&&r.hasCurrentStyleProperties()&&r.forwardFrame();let o=i&&i.easing||t.easing;t.isEmptyStep?r.applyEmptyStep(o):r.setStyles(t.styles,o,n.errors,n.options),n.previousNode=t}visitKeyframes(t,n){let r=n.currentAnimateTimings,i=n.currentTimeline.duration,o=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*o),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(i+o),n.previousNode=t}visitQuery(t,n){let r=n.currentTimeline.currentTime,i=t.options||{},o=i.delay?tn(i.delay):0;o&&(n.previousNode.type===O.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Za);let s=r,a=n.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!i.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{n.currentQueryIndex=u;let d=n.createSubContext(t.options,c);o&&d.delayNextStep(o),c===n.element&&(l=d.currentTimeline),Ke(this,t.animation,d),d.currentTimeline.applyStylesToKeyframe();let g=d.currentTimeline.currentTime;s=Math.max(s,g)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=t}visitStagger(t,n){let r=n.parentContext,i=n.currentTimeline,o=t.timings,s=Math.abs(o.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(o.duration<0?"reverse":o.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let u=n.currentTimeline;l&&u.delayNextStep(l);let d=u.currentTime;Ke(this,t.animation,n),n.previousNode=t,r.currentStaggerTime=i.currentTime-d+(i.startTime-r.currentTimeline.startTime)}},Za={},Id=class e{constructor(t,n,r,i,o,s,a,l){this._driver=t,this.element=n,this.subInstructions=r,this._enterClassName=i,this._leaveClassName=o,this.errors=s,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Za,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new Ka(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,n){if(!t)return;let r=t,i=this.options;r.duration!=null&&(i.duration=tn(r.duration)),r.delay!=null&&(i.delay=tn(r.delay));let o=r.params;if(o){let s=i.params;s||(s=this.options.params={}),Object.keys(o).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=Gi(o[a],s,this.errors))})}}_copyOptions(){let t={};if(this.options){let n=this.options.params;if(n){let r=t.params={};Object.keys(n).forEach(i=>{r[i]=n[i]})}}return t}createSubContext(t=null,n,r){let i=n||this.element,o=new e(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,r||0));return o.previousNode=this.previousNode,o.currentAnimateTimings=this.currentAnimateTimings,o.options=this._copyOptions(),o.updateOptions(t),o.currentQueryIndex=this.currentQueryIndex,o.currentQueryTotal=this.currentQueryTotal,o.parentContext=this,this.subContextCount++,o}transformIntoNewTimeline(t){return this.previousNode=Za,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,n,r){let i={duration:n??t.duration,delay:this.currentTimeline.currentTime+(r??0)+t.delay,easing:""},o=new Md(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,i,t.stretchStartingKeyframe);return this.timelines.push(o),i}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,n,r,i,o,s){let a=[];if(i&&a.push(this.element),t.length>0){t=t.replace(Xb,"."+this._enterClassName),t=t.replace(e_,"."+this._leaveClassName);let l=r!=1,c=this._driver.query(this.element,t,l);r!==0&&(c=r<0?c.slice(c.length+r,c.length):c.slice(0,r)),a.push(...c)}return!o&&a.length==0&&s.push(pb(n)),a}},Ka=class e{constructor(t,n,r,i){this._driver=t,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+t),n&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,n){return this.applyStylesToKeyframe(),new e(this._driver,t,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Qb,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,n){this._localTimelineStyles.set(t,n),this._globalTimelineStyles.set(t,n),this._styleSummary.set(t,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||xt),this._currentKeyframe.set(n,xt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,n,r,i){n&&this._previousKeyframe.set("easing",n);let o=i&&i.params||{},s=t_(t,this._globalTimelineStyles);for(let[a,l]of s){let c=Gi(l,o,r);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??xt),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,n)=>{this._currentKeyframe.set(n,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,t)}))}snapshotCurrentStyles(){for(let[t,n]of this._localTimelineStyles)this._pendingStyles.set(t,n),this._updateStyle(t,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let n in this._currentKeyframe)t.push(n);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((n,r)=>{let i=this._styleSummary.get(r);(!i||n.time>i.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,d)=>{u===La?t.add(d):u===xt&&n.add(d)}),r||c.set("offset",l/this.duration),i.push(c)});let o=[...t.values()],s=[...n.values()];if(r){let a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return jd(this.element,i,o,s,this.duration,this.startTime,this.easing,!1)}},Md=class extends Ka{constructor(t,n,r,i,o,s,a=!1){super(t,n,s.delay),this.keyframes=r,this.preStyleProps=i,this.postStyleProps=o,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:n,duration:r,easing:i}=this.timings;if(this._stretchStartingKeyframe&&n){let o=[],s=r+n,a=n/s,l=new Map(t[0]);l.set("offset",0),o.push(l);let c=new Map(t[0]);c.set("offset",P0(a)),o.push(c);let u=t.length-1;for(let d=1;d<=u;d++){let g=new Map(t[d]),f=g.get("offset"),m=n+f*r;g.set("offset",P0(m/s)),o.push(g)}r=s,n=0,i="",t=o}return jd(this.element,t,this.preStyleProps,this.postStyleProps,r,n,i,!0)}};function P0(e,t=3){let n=Math.pow(10,t-1);return Math.round(e*n)/n}function t_(e,t){let n=new Map,r;return e.forEach(i=>{if(i==="*"){r??=t.keys();for(let o of r)n.set(o,xt)}else for(let[o,s]of i)n.set(o,s)}),n}function k0(e,t,n,r,i,o,s,a,l,c,u,d,g){return{type:0,element:e,triggerName:t,isRemovalTransition:i,fromState:n,fromStyles:o,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:d,errors:g}}var pd={},Qa=class{constructor(t,n,r){this._triggerName=t,this.ast=n,this._stateStyles=r}match(t,n,r,i){return n_(this.ast.matchers,t,n,r,i)}buildStyles(t,n,r){let i=this._stateStyles.get("*");return t!==void 0&&(i=this._stateStyles.get(t?.toString())||i),i?i.buildStyles(n,r):new Map}build(t,n,r,i,o,s,a,l,c,u){let d=[],g=this.ast.options&&this.ast.options.params||pd,f=a&&a.params||pd,m=this.buildStyles(r,f,d),y=l&&l.params||pd,D=this.buildStyles(i,y,d),S=new Set,H=new Map,A=new Map,W=i==="void",ce={params:J0(y,g),delay:this.ast.options?.delay},te=u?[]:X0(t,n,this.ast.animation,o,s,m,D,ce,c,d),se=0;return te.forEach(be=>{se=Math.max(be.duration+be.delay,se)}),d.length?k0(n,this._triggerName,r,i,W,m,D,[],[],H,A,se,d):(te.forEach(be=>{let Ut=be.element,on=Qe(H,Ut,new Set);be.preStyleProps.forEach(Nn=>on.add(Nn));let Pf=Qe(A,Ut,new Set);be.postStyleProps.forEach(Nn=>Pf.add(Nn)),Ut!==n&&S.add(Ut)}),k0(n,this._triggerName,r,i,W,m,D,te,[...S.values()],H,A,se))}};function n_(e,t,n,r,i){return e.some(o=>o(t,n,r,i))}function J0(e,t){let n=E({},t);return Object.entries(e).forEach(([r,i])=>{i!=null&&(n[r]=i)}),n}var Sd=class{constructor(t,n,r){this.styles=t,this.defaultParams=n,this.normalizer=r}buildStyles(t,n){let r=new Map,i=J0(t,this.defaultParams);return this.styles.styles.forEach(o=>{typeof o!="string"&&o.forEach((s,a)=>{s&&(s=Gi(s,i,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function r_(e,t,n){return new Ad(e,t,n)}var Ad=class{constructor(t,n,r){this.name=t,this.ast=n,this._normalizer=r,this.transitionFactories=[],this.states=new Map,n.states.forEach(i=>{let o=i.options&&i.options.params||{};this.states.set(i.name,new Sd(i.style,o,r))}),L0(this.states,"true","1"),L0(this.states,"false","0"),n.transitions.forEach(i=>{this.transitionFactories.push(new Qa(t,i,this.states))}),this.fallbackTransition=i_(t,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,n,r,i){return this.transitionFactories.find(s=>s.match(t,n,r,i))||null}matchStyles(t,n,r){return this.fallbackTransition.buildStyles(t,n,r)}};function i_(e,t,n){let r=[(s,a)=>!0],i={type:O.Sequence,steps:[],options:null},o={type:O.Transition,animation:i,matchers:r,options:null,queryCount:0,depCount:0};return new Qa(e,o,t)}function L0(e,t,n){e.has(t)?e.has(n)||e.set(n,e.get(t)):e.has(n)&&e.set(t,e.get(n))}var o_=new Wi,Td=class{constructor(t,n,r){this.bodyNode=t,this._driver=n,this._normalizer=r,this._animations=new Map,this._playersById=new Map,this.players=[]}register(t,n){let r=[],i=[],o=Y0(this._driver,n,r,i);if(r.length)throw Cb(r);i.length&&void 0,this._animations.set(t,o)}_buildPlayer(t,n,r){let i=t.element,o=z0(this._normalizer,t.keyframes,n,r);return this._driver.animate(i,o,t.duration,t.delay,t.easing,[],!0)}create(t,n,r={}){let i=[],o=this._animations.get(t),s,a=new Map;if(o?(s=X0(this._driver,n,o,Z0,vd,new Map,new Map,r,o_,i),s.forEach(u=>{let d=Qe(a,u.element,new Map);u.postStyleProps.forEach(g=>d.set(g,null))})):(i.push(wb()),s=[]),i.length)throw Db(i);a.forEach((u,d)=>{u.forEach((g,f)=>{u.set(f,this._driver.computeStyle(d,f,xt))})});let l=s.map(u=>{let d=a.get(u.element);return this._buildPlayer(u,new Map,d)}),c=An(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let n=this._getPlayer(t);n.destroy(),this._playersById.delete(t);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(t){let n=this._playersById.get(t);if(!n)throw Eb(t);return n}listen(t,n,r,i){let o=Ud(n,"","","");return Od(this._getPlayer(t),r,o,i),()=>{}}command(t,n,r,i){if(r=="register"){this.register(t,i[0]);return}if(r=="create"){let s=i[0]||{};this.create(t,n,s);return}let o=this._getPlayer(t);switch(r){case"play":o.play();break;case"pause":o.pause();break;case"reset":o.reset();break;case"restart":o.restart();break;case"finish":o.finish();break;case"init":o.init();break;case"setPosition":o.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(t);break}}},V0="ng-animate-queued",s_=".ng-animate-queued",gd="ng-animate-disabled",a_=".ng-animate-disabled",l_="ng-star-inserted",c_=".ng-star-inserted",u_=[],e1={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},d_={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},vt="__ng_removed",qi=class{get params(){return this.options.params}constructor(t,n=""){this.namespaceId=n;let r=t&&t.hasOwnProperty("value"),i=r?t.value:t;if(this.value=h_(i),r){let o=t,{value:s}=o,a=Al(o,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let n=t.params;if(n){let r=this.options.params;Object.keys(n).forEach(i=>{r[i]==null&&(r[i]=n[i])})}}},zi="void",md=new qi(zi),xd=class{constructor(t,n,r){this.id=t,this.hostElement=n,this._engine=r,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+t,ct(n,this._hostClassName)}listen(t,n,r,i){if(!this._triggers.has(n))throw bb(r,n);if(r==null||r.length==0)throw _b(n);if(!p_(r))throw Ib(r,n);let o=Qe(this._elementListeners,t,[]),s={name:n,phase:r,callback:i};o.push(s);let a=Qe(this._engine.statesByElement,t,new Map);return a.has(n)||(ct(t,Va),ct(t,Va+"-"+n),a.set(n,md)),()=>{this._engine.afterFlush(()=>{let l=o.indexOf(s);l>=0&&o.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(t,n){return this._triggers.has(t)?!1:(this._triggers.set(t,n),!0)}_getTrigger(t){let n=this._triggers.get(t);if(!n)throw Mb(t);return n}trigger(t,n,r,i=!0){let o=this._getTrigger(n),s=new Zi(this.id,n,t),a=this._engine.statesByElement.get(t);a||(ct(t,Va),ct(t,Va+"-"+n),this._engine.statesByElement.set(t,a=new Map));let l=a.get(n),c=new qi(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(n,c),l||(l=md),!(c.value===zi)&&l.value===c.value){if(!y_(l.params,c.params)){let y=[],D=o.matchStyles(l.value,l.params,y),S=o.matchStyles(c.value,c.params,y);y.length?this._engine.reportError(y):this._engine.afterFlush(()=>{er(t,D),Nt(t,S)})}return}let g=Qe(this._engine.playersByElement,t,[]);g.forEach(y=>{y.namespaceId==this.id&&y.triggerName==n&&y.queued&&y.destroy()});let f=o.matchTransition(l.value,c.value,t,c.params),m=!1;if(!f){if(!i)return;f=o.fallbackTransition,m=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:n,transition:f,fromState:l,toState:c,player:s,isFallbackTransition:m}),m||(ct(t,V0),s.onStart(()=>{Zr(t,V0)})),s.onDone(()=>{let y=this.players.indexOf(s);y>=0&&this.players.splice(y,1);let D=this._engine.playersByElement.get(t);if(D){let S=D.indexOf(s);S>=0&&D.splice(S,1)}}),this.players.push(s),g.push(s),s}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(n=>n.delete(t)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(i=>i.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let n=this._engine.playersByElement.get(t);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,n){let r=this._engine.driver.query(t,za,!0);r.forEach(i=>{if(i[vt])return;let o=this._engine.fetchNamespacesByElement(i);o.size?o.forEach(s=>s.triggerLeaveAnimation(i,n,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(t,n,r,i){let o=this._engine.statesByElement.get(t),s=new Map;if(o){let a=[];if(o.forEach((l,c)=>{if(s.set(c,l.value),this._triggers.has(c)){let u=this.trigger(t,c,zi,i);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,n,s),r&&An(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let n=this._elementListeners.get(t),r=this._engine.statesByElement.get(t);if(n&&r){let i=new Set;n.forEach(o=>{let s=o.name;if(i.has(s))return;i.add(s);let l=this._triggers.get(s).fallbackTransition,c=r.get(s)||md,u=new qi(zi),d=new Zi(this.id,s,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:s,transition:l,fromState:c,toState:u,player:d,isFallbackTransition:!0})})}}removeNode(t,n){let r=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,n),this.triggerLeaveAnimation(t,n,!0))return;let i=!1;if(r.totalAnimations){let o=r.players.length?r.playersByQueriedElement.get(t):[];if(o&&o.length)i=!0;else{let s=t;for(;s=s.parentNode;)if(r.statesByElement.get(s)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(t),i)r.markElementAsRemoved(this.id,t,!1,n);else{let o=t[vt];(!o||o===e1)&&(r.afterFlush(()=>this.clearElementCache(t)),r.destroyInnerAnimations(t),r._onRemovalComplete(t,n))}}insertNode(t,n){ct(t,this._hostClassName)}drainQueuedTransitions(t){let n=[];return this._queue.forEach(r=>{let i=r.player;if(i.destroyed)return;let o=r.element,s=this._elementListeners.get(o);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=Ud(o,r.triggerName,r.fromState.value,r.toState.value);l._data=t,Od(r.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):n.push(r)}),this._queue=[],n.sort((r,i)=>{let o=r.transition.ast.depCount,s=i.transition.ast.depCount;return o==0||s==0?o-s:this._engine.driver.containsElement(r.element,i.element)?1:-1})}destroy(t){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},Fd=class{_onRemovalComplete(t,n){this.onRemovalComplete(t,n)}constructor(t,n,r){this.bodyNode=t,this.driver=n,this._normalizer=r,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(i,o)=>{}}get queuedPlayers(){let t=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&t.push(r)})}),t}createNamespace(t,n){let r=new xd(t,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[t]=r}_balanceNamespaceList(t,n){let r=this._namespaceList,i=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=i.get(a);if(l){let c=r.indexOf(l);r.splice(c+1,0,t),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(t)}else r.push(t);return i.set(n,t),t}register(t,n){let r=this._namespaceLookup[t];return r||(r=this.createNamespace(t,n)),r}registerTrigger(t,n,r){let i=this._namespaceLookup[t];i&&i.register(n,r)&&this.totalAnimations++}destroy(t,n){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(t);this.namespacesByHostElement.delete(r.hostElement);let i=this._namespaceList.indexOf(r);i>=0&&this._namespaceList.splice(i,1),r.destroy(n),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let n=new Set,r=this.statesByElement.get(t);if(r){for(let i of r.values())if(i.namespaceId){let o=this._fetchNamespace(i.namespaceId);o&&n.add(o)}}return n}trigger(t,n,r,i){if($a(n)){let o=this._fetchNamespace(t);if(o)return o.trigger(n,r,i),!0}return!1}insertNode(t,n,r,i){if(!$a(n))return;let o=n[vt];if(o&&o.setForRemoval){o.setForRemoval=!1,o.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(t){let s=this._fetchNamespace(t);s&&s.insertNode(n,r)}i&&this.collectEnterElement(n)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,n){n?this.disabledNodes.has(t)||(this.disabledNodes.add(t),ct(t,gd)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Zr(t,gd))}removeNode(t,n,r){if($a(n)){let i=t?this._fetchNamespace(t):null;i?i.removeNode(n,r):this.markElementAsRemoved(t,n,!1,r);let o=this.namespacesByHostElement.get(n);o&&o.id!==t&&o.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(t,n,r,i,o){this.collectedLeaveElements.push(n),n[vt]={namespaceId:t,setForRemoval:i,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:o}}listen(t,n,r,i,o){return $a(n)?this._fetchNamespace(t).listen(n,r,i,o):()=>{}}_buildInstruction(t,n,r,i,o){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,r,i,t.fromState.options,t.toState.options,n,o)}destroyInnerAnimations(t){let n=this.driver.query(t,za,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(t,Cd,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(t){let n=this.playersByElement.get(t);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(t){let n=this.playersByQueriedElement.get(t);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return An(this.players).onDone(()=>t());t()})}processLeaveNode(t){let n=t[vt];if(n&&n.setForRemoval){if(t[vt]=e1,n.namespaceId){this.destroyInnerAnimations(t);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(t)}this._onRemovalComplete(t,n.setForRemoval)}t.classList?.contains(gd)&&this.markElementAsDisabled(t,!1),this.driver.query(t,a_,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(t=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,i)=>this._balanceNamespaceList(r,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let i=this.collectedEnterElements[r];ct(i,l_)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,t)}finally{for(let i=0;i<r.length;i++)r[i]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let i=this.collectedLeaveElements[r];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?An(n).onDone(()=>{r.forEach(i=>i())}):r.forEach(i=>i())}}reportError(t){throw Sb(t)}_flushAnimations(t,n){let r=new Wi,i=[],o=new Map,s=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(M=>{u.add(M);let T=this.driver.query(M,s_,!0);for(let x=0;x<T.length;x++)u.add(T[x])});let d=this.bodyNode,g=Array.from(this.statesByElement.keys()),f=$0(g,this.collectedEnterElements),m=new Map,y=0;f.forEach((M,T)=>{let x=Z0+y++;m.set(T,x),M.forEach(q=>ct(q,x))});let D=[],S=new Set,H=new Set;for(let M=0;M<this.collectedLeaveElements.length;M++){let T=this.collectedLeaveElements[M],x=T[vt];x&&x.setForRemoval&&(D.push(T),S.add(T),x.hasAnimation?this.driver.query(T,c_,!0).forEach(q=>S.add(q)):H.add(T))}let A=new Map,W=$0(g,Array.from(S));W.forEach((M,T)=>{let x=vd+y++;A.set(T,x),M.forEach(q=>ct(q,x))}),t.push(()=>{f.forEach((M,T)=>{let x=m.get(T);M.forEach(q=>Zr(q,x))}),W.forEach((M,T)=>{let x=A.get(T);M.forEach(q=>Zr(q,x))}),D.forEach(M=>{this.processLeaveNode(M)})});let ce=[],te=[];for(let M=this._namespaceList.length-1;M>=0;M--)this._namespaceList[M].drainQueuedTransitions(n).forEach(x=>{let q=x.player,_e=x.element;if(ce.push(q),this.collectedEnterElements.length){let xe=_e[vt];if(xe&&xe.setForMove){if(xe.previousTriggersValues&&xe.previousTriggersValues.has(x.triggerName)){let Rn=xe.previousTriggersValues.get(x.triggerName),Je=this.statesByElement.get(x.element);if(Je&&Je.has(x.triggerName)){let Eo=Je.get(x.triggerName);Eo.value=Rn,Je.set(x.triggerName,Eo)}}q.destroy();return}}let wt=!d||!this.driver.containsElement(d,_e),He=A.get(_e),sn=m.get(_e),le=this._buildInstruction(x,r,sn,He,wt);if(le.errors&&le.errors.length){te.push(le);return}if(wt){q.onStart(()=>er(_e,le.fromStyles)),q.onDestroy(()=>Nt(_e,le.toStyles)),i.push(q);return}if(x.isFallbackTransition){q.onStart(()=>er(_e,le.fromStyles)),q.onDestroy(()=>Nt(_e,le.toStyles)),i.push(q);return}let Vf=[];le.timelines.forEach(xe=>{xe.stretchStartingKeyframe=!0,this.disabledNodes.has(xe.element)||Vf.push(xe)}),le.timelines=Vf,r.append(_e,le.timelines);let Sy={instruction:le,player:q,element:_e};s.push(Sy),le.queriedElements.forEach(xe=>Qe(a,xe,[]).push(q)),le.preStyleProps.forEach((xe,Rn)=>{if(xe.size){let Je=l.get(Rn);Je||l.set(Rn,Je=new Set),xe.forEach((Eo,Sl)=>Je.add(Sl))}}),le.postStyleProps.forEach((xe,Rn)=>{let Je=c.get(Rn);Je||c.set(Rn,Je=new Set),xe.forEach((Eo,Sl)=>Je.add(Sl))})});if(te.length){let M=[];te.forEach(T=>{M.push(Ab(T.triggerName,T.errors))}),ce.forEach(T=>T.destroy()),this.reportError(M)}let se=new Map,be=new Map;s.forEach(M=>{let T=M.element;r.has(T)&&(be.set(T,T),this._beforeAnimationBuild(M.player.namespaceId,M.instruction,se))}),i.forEach(M=>{let T=M.element;this._getPreviousPlayers(T,!1,M.namespaceId,M.triggerName,null).forEach(q=>{Qe(se,T,[]).push(q),q.destroy()})});let Ut=D.filter(M=>H0(M,l,c)),on=new Map;B0(on,this.driver,H,c,xt).forEach(M=>{H0(M,l,c)&&Ut.push(M)});let Nn=new Map;f.forEach((M,T)=>{B0(Nn,this.driver,new Set(M),l,La)}),Ut.forEach(M=>{let T=on.get(M),x=Nn.get(M);on.set(M,new Map([...T?.entries()??[],...x?.entries()??[]]))});let Ml=[],kf=[],Lf={};s.forEach(M=>{let{element:T,player:x,instruction:q}=M;if(r.has(T)){if(u.has(T)){x.onDestroy(()=>Nt(T,q.toStyles)),x.disabled=!0,x.overrideTotalTime(q.totalTime),i.push(x);return}let _e=Lf;if(be.size>1){let He=T,sn=[];for(;He=He.parentNode;){let le=be.get(He);if(le){_e=le;break}sn.push(He)}sn.forEach(le=>be.set(le,_e))}let wt=this._buildAnimation(x.namespaceId,q,se,o,Nn,on);if(x.setRealPlayer(wt),_e===Lf)Ml.push(x);else{let He=this.playersByElement.get(_e);He&&He.length&&(x.parentPlayer=An(He)),i.push(x)}}else er(T,q.fromStyles),x.onDestroy(()=>Nt(T,q.toStyles)),kf.push(x),u.has(T)&&i.push(x)}),kf.forEach(M=>{let T=o.get(M.element);if(T&&T.length){let x=An(T);M.setRealPlayer(x)}}),i.forEach(M=>{M.parentPlayer?M.syncPlayerEvents(M.parentPlayer):M.destroy()});for(let M=0;M<D.length;M++){let T=D[M],x=T[vt];if(Zr(T,vd),x&&x.hasAnimation)continue;let q=[];if(a.size){let wt=a.get(T);wt&&wt.length&&q.push(...wt);let He=this.driver.query(T,Cd,!0);for(let sn=0;sn<He.length;sn++){let le=a.get(He[sn]);le&&le.length&&q.push(...le)}}let _e=q.filter(wt=>!wt.destroyed);_e.length?g_(this,T,_e):this.processLeaveNode(T)}return D.length=0,Ml.forEach(M=>{this.players.push(M),M.onDone(()=>{M.destroy();let T=this.players.indexOf(M);this.players.splice(T,1)}),M.play()}),Ml}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,n,r,i,o){let s=[];if(n){let a=this.playersByQueriedElement.get(t);a&&(s=a)}else{let a=this.playersByElement.get(t);if(a){let l=!o||o==zi;a.forEach(c=>{c.queued||!l&&c.triggerName!=i||s.push(c)})}}return(r||i)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||i&&i!=a.triggerName))),s}_beforeAnimationBuild(t,n,r){let i=n.triggerName,o=n.element,s=n.isRemovalTransition?void 0:t,a=n.isRemovalTransition?void 0:i;for(let l of n.timelines){let c=l.element,u=c!==o,d=Qe(r,c,[]);this._getPreviousPlayers(c,u,s,a,n.toState).forEach(f=>{let m=f.getRealPlayer();m.beforeDestroy&&m.beforeDestroy(),f.destroy(),d.push(f)})}er(o,n.fromStyles)}_buildAnimation(t,n,r,i,o,s){let a=n.triggerName,l=n.element,c=[],u=new Set,d=new Set,g=n.timelines.map(m=>{let y=m.element;u.add(y);let D=y[vt];if(D&&D.removedBeforeQueried)return new Sn(m.duration,m.delay);let S=y!==l,H=m_((r.get(y)||u_).map(se=>se.getRealPlayer())).filter(se=>{let be=se;return be.element?be.element===y:!1}),A=o.get(y),W=s.get(y),ce=z0(this._normalizer,m.keyframes,A,W),te=this._buildPlayer(m,ce,H);if(m.subTimeline&&i&&d.add(y),S){let se=new Zi(t,a,y);se.setRealPlayer(te),c.push(se)}return te});c.forEach(m=>{Qe(this.playersByQueriedElement,m.element,[]).push(m),m.onDone(()=>f_(this.playersByQueriedElement,m.element,m))}),u.forEach(m=>ct(m,R0));let f=An(g);return f.onDestroy(()=>{u.forEach(m=>Zr(m,R0)),Nt(l,n.toStyles)}),d.forEach(m=>{Qe(i,m,[]).push(f)}),f}_buildPlayer(t,n,r){return n.length>0?this.driver.animate(t.element,n,t.duration,t.delay,t.easing,r):new Sn(t.duration,t.delay)}},Zi=class{constructor(t,n,r){this.namespaceId=t,this.triggerName=n,this.element=r,this._player=new Sn,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((n,r)=>{n.forEach(i=>Od(t,r,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let n=this._player;n.triggerCallback&&t.onStart(()=>n.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,n){Qe(this._queuedCallbacks,t,[]).push(n)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let n=this._player;n.triggerCallback&&n.triggerCallback(t)}};function f_(e,t,n){let r=e.get(t);if(r){if(r.length){let i=r.indexOf(n);r.splice(i,1)}r.length==0&&e.delete(t)}return r}function h_(e){return e??null}function $a(e){return e&&e.nodeType===1}function p_(e){return e=="start"||e=="done"}function j0(e,t){let n=e.style.display;return e.style.display=t??"none",n}function B0(e,t,n,r,i){let o=[];n.forEach(l=>o.push(j0(l)));let s=[];r.forEach((l,c)=>{let u=new Map;l.forEach(d=>{let g=t.computeStyle(c,d,i);u.set(d,g),(!g||g.length==0)&&(c[vt]=d_,s.push(c))}),e.set(c,u)});let a=0;return n.forEach(l=>j0(l,o[a++])),s}function $0(e,t){let n=new Map;if(e.forEach(a=>n.set(a,[])),t.length==0)return n;let r=1,i=new Set(t),o=new Map;function s(a){if(!a)return r;let l=o.get(a);if(l)return l;let c=a.parentNode;return n.has(c)?l=c:i.has(c)?l=r:l=s(c),o.set(a,l),l}return t.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function ct(e,t){e.classList?.add(t)}function Zr(e,t){e.classList?.remove(t)}function g_(e,t,n){An(n).onDone(()=>e.processLeaveNode(t))}function m_(e){let t=[];return t1(e,t),t}function t1(e,t){for(let n=0;n<e.length;n++){let r=e[n];r instanceof $i?t1(r.players,t):t.push(r)}}function y_(e,t){let n=Object.keys(e),r=Object.keys(t);if(n.length!=r.length)return!1;for(let i=0;i<n.length;i++){let o=n[i];if(!t.hasOwnProperty(o)||e[o]!==t[o])return!1}return!0}function H0(e,t,n){let r=n.get(e);if(!r)return!1;let i=t.get(e);return i?r.forEach(o=>i.add(o)):t.set(e,r),n.delete(e),!0}var Kr=class{constructor(t,n,r){this._driver=n,this._normalizer=r,this._triggerCache={},this.onRemovalComplete=(i,o)=>{},this._transitionEngine=new Fd(t.body,n,r),this._timelineEngine=new Td(t.body,n,r),this._transitionEngine.onRemovalComplete=(i,o)=>this.onRemovalComplete(i,o)}registerTrigger(t,n,r,i,o){let s=t+"-"+i,a=this._triggerCache[s];if(!a){let l=[],c=[],u=Y0(this._driver,o,l,c);if(l.length)throw yb(i,l);c.length&&void 0,a=r_(i,u,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,i,a)}register(t,n){this._transitionEngine.register(t,n)}destroy(t,n){this._transitionEngine.destroy(t,n)}onInsert(t,n,r,i){this._transitionEngine.insertNode(t,n,r,i)}onRemove(t,n,r){this._transitionEngine.removeNode(t,n,r)}disableAnimations(t,n){this._transitionEngine.markElementAsDisabled(t,n)}process(t,n,r,i){if(r.charAt(0)=="@"){let[o,s]=F0(r),a=i;this._timelineEngine.command(o,n,s,a)}else this._transitionEngine.trigger(t,n,r,i)}listen(t,n,r,i,o){if(r.charAt(0)=="@"){let[s,a]=F0(r);return this._timelineEngine.listen(s,n,a,o)}return this._transitionEngine.listen(t,n,r,i,o)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function v_(e,t){let n=null,r=null;return Array.isArray(t)&&t.length?(n=yd(t[0]),t.length>1&&(r=yd(t[t.length-1]))):t instanceof Map&&(n=yd(t)),n||r?new Nd(e,n,r):null}var Nd=class e{static{this.initialStylesByElement=new WeakMap}constructor(t,n,r){this._element=t,this._startStyles=n,this._endStyles=r,this._state=0;let i=e.initialStylesByElement.get(t);i||e.initialStylesByElement.set(t,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&Nt(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Nt(this._element,this._initialStyles),this._endStyles&&(Nt(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(e.initialStylesByElement.delete(this._element),this._startStyles&&(er(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(er(this._element,this._endStyles),this._endStyles=null),Nt(this._element,this._initialStyles),this._state=3)}};function yd(e){let t=null;return e.forEach((n,r)=>{C_(r)&&(t=t||new Map,t.set(r,n))}),t}function C_(e){return e==="display"||e==="position"}var Ya=class{constructor(t,n,r,i){this.element=t,this.keyframes=n,this.options=r,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let n=[];return t.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(t,n,r){return t.animate(this._convertKeyframesToObject(n),r)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,i)=>{i!=="offset"&&t.set(i,this._finished?r:Vd(this.element,i))}),this.currentSnapshot=t}triggerCallback(t){let n=t==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Xa=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,n){return G0(t,n)}getParentElement(t){return Pd(t)}query(t,n,r){return W0(t,n,r)}computeStyle(t,n,r){return Vd(t,n)}animate(t,n,r,i,o,s=[]){let a=i==0?"both":"forwards",l={duration:r,delay:i,fill:a};o&&(l.easing=o);let c=new Map,u=s.filter(f=>f instanceof Ya);Vb(r,i)&&u.forEach(f=>{f.currentSnapshot.forEach((m,y)=>c.set(y,m))});let d=Pb(n).map(f=>new Map(f));d=jb(t,d,c);let g=v_(t,d);return new Ya(t,d,l,g)}};var Ha="@",n1="@.disabled",Ja=class{constructor(t,n,r,i){this.namespaceId=t,this.delegate=n,this.engine=r,this._onDestroy=i,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,n){return this.delegate.createElement(t,n)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,n){this.delegate.appendChild(t,n),this.engine.onInsert(this.namespaceId,n,t,!1)}insertBefore(t,n,r,i=!0){this.delegate.insertBefore(t,n,r),this.engine.onInsert(this.namespaceId,n,t,i)}removeChild(t,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(t,n){return this.delegate.selectRootElement(t,n)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,n,r,i){this.delegate.setAttribute(t,n,r,i)}removeAttribute(t,n,r){this.delegate.removeAttribute(t,n,r)}addClass(t,n){this.delegate.addClass(t,n)}removeClass(t,n){this.delegate.removeClass(t,n)}setStyle(t,n,r,i){this.delegate.setStyle(t,n,r,i)}removeStyle(t,n,r){this.delegate.removeStyle(t,n,r)}setProperty(t,n,r){n.charAt(0)==Ha&&n==n1?this.disableAnimations(t,!!r):this.delegate.setProperty(t,n,r)}setValue(t,n){this.delegate.setValue(t,n)}listen(t,n,r){return this.delegate.listen(t,n,r)}disableAnimations(t,n){this.engine.disableAnimations(t,n)}},Rd=class extends Ja{constructor(t,n,r,i,o){super(n,r,i,o),this.factory=t,this.namespaceId=n}setProperty(t,n,r){n.charAt(0)==Ha?n.charAt(1)=="."&&n==n1?(r=r===void 0?!0:!!r,this.disableAnimations(t,r)):this.engine.process(this.namespaceId,t,n.slice(1),r):this.delegate.setProperty(t,n,r)}listen(t,n,r){if(n.charAt(0)==Ha){let i=w_(t),o=n.slice(1),s="";return o.charAt(0)!=Ha&&([o,s]=D_(o)),this.engine.listen(this.namespaceId,i,o,s,a=>{let l=a._data||-1;this.factory.scheduleListenerCallback(l,r,a)})}return this.delegate.listen(t,n,r)}};function w_(e){switch(e){case"body":return document.body;case"document":return document;case"window":return window;default:return e}}function D_(e){let t=e.indexOf("."),n=e.substring(0,t),r=e.slice(t+1);return[n,r]}var el=class{constructor(t,n,r){this.delegate=t,this.engine=n,this._zone=r,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,n.onRemovalComplete=(i,o)=>{o?.removeChild(null,i)}}createRenderer(t,n){let r="",i=this.delegate.createRenderer(t,n);if(!t||!n?.data?.animation){let c=this._rendererCache,u=c.get(i);if(!u){let d=()=>c.delete(i);u=new Ja(r,i,this.engine,d),c.set(i,u)}return u}let o=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(o,s,t,c.name,c)};return n.data.animation.forEach(a),new Rd(this,s,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,n,r){if(t>=0&&t<this._microtaskId){this._zone.run(()=>n(r));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(o=>{let[s,a]=o;s(a)}),this._animationCallbacksBuffer=[]})}),i.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};var b_=(()=>{class e extends Kr{constructor(n,r,i){super(n,r,i)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(r){return new(r||e)(_(Ee),_(tr),_(nr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function __(){return new Wa}function I_(e,t,n){return new el(e,t,n)}var i1=[{provide:nr,useFactory:__},{provide:Kr,useClass:b_},{provide:vn,useFactory:I_,deps:[Na,Kr,X]}],r1=[{provide:tr,useFactory:()=>new Xa},{provide:Cu,useValue:"BrowserAnimations"},...i1],M_=[{provide:tr,useClass:kd},{provide:Cu,useValue:"NoopAnimations"},...i1],o1=(()=>{class e{static withConfig(n){return{ngModule:e,providers:n.disableAnimations?M_:r1}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({providers:r1,imports:[Oa]})}}return e})();var S_=new I(""),A_=new I("");function u1(e){return e!=null}function d1(e){return Qn(e)?ae(e):e}function f1(e){let t={};return e.forEach(n=>{t=n!=null?E(E({},t),n):t}),Object.keys(t).length===0?null:t}function h1(e,t){return t.map(n=>n(e))}function T_(e){return!e.validate}function p1(e){return e.map(t=>T_(t)?t:n=>t.validate(n))}function x_(e){if(!e)return null;let t=e.filter(u1);return t.length==0?null:function(n){return f1(h1(n,t))}}function Gd(e){return e!=null?x_(p1(e)):null}function F_(e){if(!e)return null;let t=e.filter(u1);return t.length==0?null:function(n){let r=h1(n,t).map(d1);return Wl(r).pipe(P(f1))}}function Wd(e){return e!=null?F_(p1(e)):null}function s1(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function N_(e){return e._rawValidators}function R_(e){return e._rawAsyncValidators}function Bd(e){return e?Array.isArray(e)?e:[e]:[]}function nl(e,t){return Array.isArray(e)?e.includes(t):e===t}function a1(e,t){let n=Bd(t);return Bd(e).forEach(i=>{nl(n,i)||n.push(i)}),n}function l1(e,t){return Bd(t).filter(n=>!nl(e,n))}var $d=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Gd(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Wd(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},eo=class extends $d{get formDirective(){return null}get path(){return null}};var Hd=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},O_={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},R9=Z(E({},O_),{"[class.ng-submitted]":"isSubmitted"});var g1=(()=>{class e extends Hd{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)($(eo,10))}}static{this.\u0275dir=gt({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&Ou("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[ta]})}}return e})();var Ki="VALID",tl="INVALID",Qr="PENDING",Qi="DISABLED",Xr=class{},rl=class extends Xr{constructor(t,n){super(),this.value=t,this.source=n}},Xi=class extends Xr{constructor(t,n){super(),this.pristine=t,this.source=n}},Ji=class extends Xr{constructor(t,n){super(),this.touched=t,this.source=n}},Yr=class extends Xr{constructor(t,n){super(),this.status=t,this.source=n}};function U_(e){return(qd(e)?e.validators:e)||null}function P_(e){return Array.isArray(e)?Gd(e):e||null}function k_(e,t){return(qd(t)?t.asyncValidators:e)||null}function L_(e){return Array.isArray(e)?Wd(e):e||null}function qd(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function V_(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new C(1e3,"");if(!r[n])throw new C(1001,"")}function j_(e,t,n){e._forEachChild((r,i)=>{if(n[i]===void 0)throw new C(1002,"")})}var zd=class{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Ri(()=>this.statusReactive()),this.statusReactive=Ti(void 0),this._pristine=Ri(()=>this.pristineReactive()),this.pristineReactive=Ti(!0),this._touched=Ri(()=>this.touchedReactive()),this.touchedReactive=Ti(!1),this._events=new Ie,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Zt(this.statusReactive)}set status(t){Zt(()=>this.statusReactive.set(t))}get valid(){return this.status===Ki}get invalid(){return this.status===tl}get pending(){return this.status==Qr}get disabled(){return this.status===Qi}get enabled(){return this.status!==Qi}get pristine(){return Zt(this.pristineReactive)}set pristine(t){Zt(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return Zt(this.touchedReactive)}set touched(t){Zt(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(a1(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(a1(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(l1(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(l1(t,this._rawAsyncValidators))}hasValidator(t){return nl(this._rawValidators,t)}hasAsyncValidator(t){return nl(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(Z(E({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Ji(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new Ji(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(Z(E({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Xi(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new Xi(!0,r))}markAsPending(t={}){this.status=Qr;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Yr(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(Z(E({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Qi,this.errors=null,this._forEachChild(i=>{i.disable(Z(E({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new rl(this.value,r)),this._events.next(new Yr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(Z(E({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Ki,this._forEachChild(r=>{r.enable(Z(E({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(Z(E({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ki||this.status===Qr)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new rl(this.value,n)),this._events.next(new Yr(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(Z(E({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Qi:Ki}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=Qr,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=d1(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,i)=>r&&r._find(i),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new Yr(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new Ce,this.statusChanges=new Ce}_calculateStatus(){return this._allControlsDisabled()?Qi:this.errors?tl:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Qr)?Qr:this._anyControlsHaveStatus(tl)?tl:Ki}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),i&&this._events.next(new Xi(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new Ji(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){qd(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=P_(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=L_(this._rawAsyncValidators)}},il=class extends zd{constructor(t,n,r){super(U_(n),k_(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){j_(this,!0,t),Object.keys(t).forEach(r=>{V_(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let i=this.controls[r];i&&i.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,i)=>{r.reset(t?t[i]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,i)=>((r.enabled||this.disabled)&&(n[i]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((i,o)=>{r=n(r,i,o)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var Zd=new I("CallSetDisabledState",{providedIn:"root",factory:()=>ol}),ol="always";function B_(e,t,n=ol){m1(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),H_(e,t),G_(e,t),z_(e,t),$_(e,t)}function c1(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function $_(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function m1(e,t){let n=N_(e);t.validator!==null?e.setValidators(s1(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=R_(e);t.asyncValidator!==null?e.setAsyncValidators(s1(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();c1(t._rawValidators,i),c1(t._rawAsyncValidators,i)}function H_(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&y1(e,t)})}function z_(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&y1(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function y1(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function G_(e,t){let n=(r,i)=>{t.valueAccessor.writeValue(r),i&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function W_(e,t){e==null,m1(e,t)}function q_(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}var Z_={provide:eo,useExisting:ks(()=>Kd)},Yi=Promise.resolve(),Kd=(()=>{class e extends eo{get submitted(){return Zt(this.submittedReactive)}constructor(n,r,i){super(),this.callSetDisabledState=i,this._submitted=Ri(()=>this.submittedReactive()),this.submittedReactive=Ti(!1),this._directives=new Set,this.ngSubmit=new Ce,this.form=new il({},Gd(n),Wd(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){Yi.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),B_(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){Yi.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){Yi.then(()=>{let r=this._findContainer(n.path),i=new il({});W_(i,n),r.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){Yi.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){Yi.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),q_(this.form,this._directives),this.ngSubmit.emit(n),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static{this.\u0275fac=function(r){return new(r||e)($(S_,10),$(A_,10),$(Zd,8))}}static{this.\u0275dir=gt({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&Wt("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[_m([Z_]),ta]})}}return e})();var v1=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=gt({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();var K_=new I("");var C1=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({})}}return e})();var w1=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Zd,useValue:n.callSetDisabledState??ol}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({imports:[C1]})}}return e})(),D1=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:K_,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:Zd,useValue:n.callSetDisabledState??ol}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({imports:[C1]})}}return e})();var k="primary",go=Symbol("RouteTitle"),ef=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function ii(e){return new ef(e)}function Y_(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=e[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:i}}function X_(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Rt(e[n],t[n]))return!1;return!0}function Rt(e,t){let n=e?tf(e):void 0,r=t?tf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let i;for(let o=0;o<n.length;o++)if(i=n[o],!N1(e[i],t[i]))return!1;return!0}function tf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function N1(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((i,o)=>r[o]===i)}else return e===t}function R1(e){return e.length>0?e[e.length-1]:null}function Fn(e){return Gl(e)?e:Qn(e)?ae(Promise.resolve(e)):F(e)}var J_={exact:U1,subset:P1},O1={exact:e4,subset:t4,ignored:()=>!0};function E1(e,t,n){return J_[n.paths](e.root,t.root,n.matrixParams)&&O1[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function e4(e,t){return Rt(e,t)}function U1(e,t,n){if(!ir(e.segments,t.segments)||!ll(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!U1(e.children[r],t.children[r],n))return!1;return!0}function t4(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>N1(e[n],t[n]))}function P1(e,t,n){return k1(e,t,t.segments,n)}function k1(e,t,n,r){if(e.segments.length>n.length){let i=e.segments.slice(0,n.length);return!(!ir(i,n)||t.hasChildren()||!ll(i,n,r))}else if(e.segments.length===n.length){if(!ir(e.segments,n)||!ll(e.segments,n,r))return!1;for(let i in t.children)if(!e.children[i]||!P1(e.children[i],t.children[i],r))return!1;return!0}else{let i=n.slice(0,e.segments.length),o=n.slice(e.segments.length);return!ir(e.segments,i)||!ll(e.segments,i,r)||!e.children[k]?!1:k1(e.children[k],t,o,r)}}function ll(e,t,n){return t.every((r,i)=>O1[n](e[i].parameters,r.parameters))}var rn=class{constructor(t=new ee([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ii(this.queryParams),this._queryParamMap}toString(){return i4.serialize(this)}},ee=class{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return cl(this)}},rr=class{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=ii(this.parameters),this._parameterMap}toString(){return V1(this)}};function n4(e,t){return ir(e,t)&&e.every((n,r)=>Rt(n.parameters,t[r].parameters))}function ir(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function r4(e,t){let n=[];return Object.entries(e.children).forEach(([r,i])=>{r===k&&(n=n.concat(t(i,r)))}),Object.entries(e.children).forEach(([r,i])=>{r!==k&&(n=n.concat(t(i,r)))}),n}var mo=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new oi,providedIn:"root"})}}return e})(),oi=class{parse(t){let n=new rf(t);return new rn(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${to(t.root,!0)}`,r=a4(t.queryParams),i=typeof t.fragment=="string"?`#${o4(t.fragment)}`:"";return`${n}${r}${i}`}},i4=new oi;function cl(e){return e.segments.map(t=>V1(t)).join("/")}function to(e,t){if(!e.hasChildren())return cl(e);if(t){let n=e.children[k]?to(e.children[k],!1):"",r=[];return Object.entries(e.children).forEach(([i,o])=>{i!==k&&r.push(`${i}:${to(o,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=r4(e,(r,i)=>i===k?[to(e.children[k],!1)]:[`${i}:${to(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[k]!=null?`${cl(e)}/${n[0]}`:`${cl(e)}/(${n.join("//")})`}}function L1(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function sl(e){return L1(e).replace(/%3B/gi,";")}function o4(e){return encodeURI(e)}function nf(e){return L1(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ul(e){return decodeURIComponent(e)}function b1(e){return ul(e.replace(/\+/g,"%20"))}function V1(e){return`${nf(e.path)}${s4(e.parameters)}`}function s4(e){return Object.entries(e).map(([t,n])=>`;${nf(t)}=${nf(n)}`).join("")}function a4(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(i=>`${sl(n)}=${sl(i)}`).join("&"):`${sl(n)}=${sl(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var l4=/^[^\/()?;#]+/;function Qd(e){let t=e.match(l4);return t?t[0]:""}var c4=/^[^\/()?;=#]+/;function u4(e){let t=e.match(c4);return t?t[0]:""}var d4=/^[^=?&#]+/;function f4(e){let t=e.match(d4);return t?t[0]:""}var h4=/^[^&#]+/;function p4(e){let t=e.match(h4);return t?t[0]:""}var rf=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new ee([],{}):new ee([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[k]=new ee(t,n)),r}parseSegment(){let t=Qd(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(t),new rr(ul(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=u4(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let i=Qd(this.remaining);i&&(r=i,this.capture(r))}t[ul(n)]=ul(r)}parseQueryParam(t){let n=f4(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=p4(this.remaining);s&&(r=s,this.capture(r))}let i=b1(n),o=b1(r);if(t.hasOwnProperty(i)){let s=t[i];Array.isArray(s)||(s=[s],t[i]=s),s.push(o)}else t[i]=o}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Qd(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new C(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):t&&(o=k);let s=this.parseChildren();n[o]=Object.keys(s).length===1?s[k]:new ee([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new C(4011,!1)}};function j1(e){return e.segments.length>0?new ee([],{[k]:e}):e}function B1(e){let t={};for(let[r,i]of Object.entries(e.children)){let o=B1(i);if(r===k&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))t[s]=a;else(o.segments.length>0||o.hasChildren())&&(t[r]=o)}let n=new ee(e.segments,t);return g4(n)}function g4(e){if(e.numberOfChildren===1&&e.children[k]){let t=e.children[k];return new ee(e.segments.concat(t.segments),t.children)}return e}function or(e){return e instanceof rn}function m4(e,t,n=null,r=null){let i=$1(e);return H1(i,t,n,r)}function $1(e){let t;function n(o){let s={};for(let l of o.children){let c=n(l);s[l.outlet]=c}let a=new ee(o.url,s);return o===e&&(t=a),a}let r=n(e.root),i=j1(r);return t??i}function H1(e,t,n,r){let i=e;for(;i.parent;)i=i.parent;if(t.length===0)return Yd(i,i,i,n,r);let o=y4(t);if(o.toRoot())return Yd(i,i,new ee([],{}),n,r);let s=v4(o,i,e),a=s.processChildren?io(s.segmentGroup,s.index,o.commands):G1(s.segmentGroup,s.index,o.commands);return Yd(i,s.segmentGroup,a,n,r)}function dl(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function ao(e){return typeof e=="object"&&e!=null&&e.outlets}function Yd(e,t,n,r,i){let o={};r&&Object.entries(r).forEach(([l,c])=>{o[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`});let s;e===t?s=n:s=z1(e,t,n);let a=j1(B1(s));return new rn(a,o,i)}function z1(e,t,n){let r={};return Object.entries(e.children).forEach(([i,o])=>{o===t?r[i]=n:r[i]=z1(o,t,n)}),new ee(e.segments,r)}var fl=class{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&dl(r[0]))throw new C(4003,!1);let i=r.find(ao);if(i&&i!==R1(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function y4(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new fl(!0,0,e);let t=0,n=!1,r=e.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?n=!0:a===".."?t++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new fl(n,t,r)}var ti=class{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function v4(e,t,n){if(e.isAbsolute)return new ti(t,!0,0);if(!n)return new ti(t,!1,NaN);if(n.parent===null)return new ti(n,!0,0);let r=dl(e.commands[0])?0:1,i=n.segments.length-1+r;return C4(n,i,e.numberOfDoubleDots)}function C4(e,t,n){let r=e,i=t,o=n;for(;o>i;){if(o-=i,r=r.parent,!r)throw new C(4005,!1);i=r.segments.length}return new ti(r,!1,i-o)}function w4(e){return ao(e[0])?e[0].outlets:{[k]:e}}function G1(e,t,n){if(e??=new ee([],{}),e.segments.length===0&&e.hasChildren())return io(e,t,n);let r=D4(e,t,n),i=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let o=new ee(e.segments.slice(0,r.pathIndex),{});return o.children[k]=new ee(e.segments.slice(r.pathIndex),e.children),io(o,0,i)}else return r.match&&i.length===0?new ee(e.segments,{}):r.match&&!e.hasChildren()?of(e,t,n):r.match?io(e,0,i):of(e,t,n)}function io(e,t,n){if(n.length===0)return new ee(e.segments,{});{let r=w4(n),i={};if(Object.keys(r).some(o=>o!==k)&&e.children[k]&&e.numberOfChildren===1&&e.children[k].segments.length===0){let o=io(e.children[k],t,n);return new ee(e.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=G1(e.children[o],t,s))}),Object.entries(e.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new ee(e.segments,i)}}function D4(e,t,n){let r=0,i=t,o={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(r>=n.length)return o;let s=e.segments[i],a=n[r];if(ao(a))break;let l=`${a}`,c=r<n.length-1?n[r+1]:null;if(i>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!I1(l,c,s))return o;r+=2}else{if(!I1(l,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function of(e,t,n){let r=e.segments.slice(0,t),i=0;for(;i<n.length;){let o=n[i];if(ao(o)){let l=E4(o.outlets);return new ee(r,l)}if(i===0&&dl(n[0])){let l=e.segments[t];r.push(new rr(l.path,_1(n[0]))),i++;continue}let s=ao(o)?o.outlets[k]:`${o}`,a=i<n.length-1?n[i+1]:null;s&&a&&dl(a)?(r.push(new rr(s,_1(a))),i+=2):(r.push(new rr(s,{})),i++)}return new ee(r,{})}function E4(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=of(new ee([],{}),0,r))}),t}function _1(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function I1(e,t,n){return e==n.path&&Rt(t,n.parameters)}var oo="imperative",Se=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Se||{}),ut=class{constructor(t,n){this.id=t,this.url=n}},si=class extends ut{constructor(t,n,r="imperative",i=null){super(t,n),this.type=Se.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ot=class extends ut{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=Se.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Xe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Xe||{}),hl=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(hl||{}),nn=class extends ut{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=Se.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Tn=class extends ut{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=Se.NavigationSkipped}},lo=class extends ut{constructor(t,n,r,i){super(t,n),this.error=r,this.target=i,this.type=Se.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},pl=class extends ut{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Se.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},sf=class extends ut{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Se.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},af=class extends ut{constructor(t,n,r,i,o){super(t,n),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o,this.type=Se.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},lf=class extends ut{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},cf=class extends ut{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},uf=class{constructor(t){this.route=t,this.type=Se.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},df=class{constructor(t){this.route=t,this.type=Se.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ff=class{constructor(t){this.snapshot=t,this.type=Se.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},hf=class{constructor(t){this.snapshot=t,this.type=Se.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},pf=class{constructor(t){this.snapshot=t,this.type=Se.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},gf=class{constructor(t){this.snapshot=t,this.type=Se.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},gl=class{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=Se.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},co=class{},ai=class{constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function b4(e,t){return e.providers&&!e._injector&&(e._injector=na(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Ct(e){return e.outlet||k}function _4(e,t){let n=e.filter(r=>Ct(r)===t);return n.push(...e.filter(r=>Ct(r)!==t)),n}function yo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var mf=class{get injector(){return yo(this.route?.snapshot)??this.rootInjector}set injector(t){}constructor(t){this.rootInjector=t,this.outlet=null,this.route=null,this.children=new vo(this.rootInjector),this.attachRef=null}},vo=(()=>{class e{constructor(n){this.rootInjector=n,this.contexts=new Map}onChildOutletCreated(n,r){let i=this.getOrCreateContext(n);i.outlet=r,this.contexts.set(n,i)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new mf(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)(_(Oe))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ml=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=yf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=yf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=vf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(i=>i.value).filter(i=>i!==t)}pathFromRoot(t){return vf(t,this._root).map(n=>n.value)}};function yf(e,t){if(e===t.value)return t;for(let n of t.children){let r=yf(e,n);if(r)return r}return null}function vf(e,t){if(e===t.value)return[t];for(let n of t.children){let r=vf(e,n);if(r.length)return r.unshift(t),r}return[]}var Ye=class{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function ei(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var yl=class extends ml{constructor(t,n){super(t),this.snapshot=n,Sf(this,t)}toString(){return this.snapshot.toString()}};function W1(e){let t=I4(e),n=new Ae([new rr("",{})]),r=new Ae({}),i=new Ae({}),o=new Ae({}),s=new Ae(""),a=new sr(n,r,o,s,i,k,e,t.root);return a.snapshot=t.root,new yl(new Ye(a,[]),t)}function I4(e){let t={},n={},r={},i="",o=new ni([],t,r,i,n,k,e,null,{});return new Cl("",new Ye(o,[]))}var sr=class{constructor(t,n,r,i,o,s,a,l){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(P(c=>c[go]))??F(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(P(t=>ii(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(P(t=>ii(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function vl(e,t,n="emptyOnly"){let r,{routeConfig:i}=e;return t!==null&&(n==="always"||i?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:E(E({},t.params),e.params),data:E(E({},t.data),e.data),resolve:E(E(E(E({},e.data),t.data),i?.data),e._resolvedData)}:r={params:E({},e.params),data:E({},e.data),resolve:E(E({},e.data),e._resolvedData??{})},i&&Z1(i)&&(r.resolve[go]=i.title),r}var ni=class{get title(){return this.data?.[go]}constructor(t,n,r,i,o,s,a,l,c){this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ii(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ii(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Cl=class extends ml{constructor(t,n){super(n),this.url=t,Sf(this,n)}toString(){return q1(this._root)}};function Sf(e,t){t.value._routerState=e,t.children.forEach(n=>Sf(e,n))}function q1(e){let t=e.children.length>0?` { ${e.children.map(q1).join(", ")} } `:"";return`${e.value}${t}`}function Xd(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Rt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Rt(t.params,n.params)||e.paramsSubject.next(n.params),X_(t.url,n.url)||e.urlSubject.next(n.url),Rt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Cf(e,t){let n=Rt(e.params,t.params)&&n4(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Cf(e.parent,t.parent))}function Z1(e){return typeof e.title=="string"||e.title===null}var Af=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=k,this.activateEvents=new Ce,this.deactivateEvents=new Ce,this.attachEvents=new Ce,this.detachEvents=new Ce,this.parentContexts=w(vo),this.location=w(Br),this.changeDetector=w($r),this.inputBinder=w(_l,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:i}=n.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=n;let i=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new wf(n,a,i.injector);this.activated=i.createComponent(s,{index:i.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=gt({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[kr]})}}return e})(),wf=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===sr?this.route:t===vo?this.childContexts:this.parent.get(t,n)}},_l=new I(""),M1=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,i=pi([r.queryParams,r.params,r.data]).pipe(Ve(([o,s,a],l)=>(a=E(E(E({},o),s),a),l===0?F(a):Promise.resolve(a)))).subscribe(o=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Vm(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(n,i)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function M4(e,t,n){let r=uo(e,t._root,n?n._root:void 0);return new yl(r,t)}function uo(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let i=S4(e,t,n);return new Ye(r,i)}else{if(e.shouldAttach(t.value)){let o=e.retrieve(t.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>uo(e,a)),s}}let r=A4(t.value),i=t.children.map(o=>uo(e,o));return new Ye(r,i)}}function S4(e,t,n){return t.children.map(r=>{for(let i of n.children)if(e.shouldReuseRoute(r.value,i.value.snapshot))return uo(e,r,i);return uo(e,r)})}function A4(e){return new sr(new Ae(e.url),new Ae(e.params),new Ae(e.queryParams),new Ae(e.fragment),new Ae(e.data),e.outlet,e.component,e)}var fo=class{constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},K1="ngNavigationCancelingError";function wl(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=or(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,i=Q1(!1,Xe.Redirect);return i.url=n,i.navigationBehaviorOptions=r,i}function Q1(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[K1]=!0,n.cancellationCode=t,n}function T4(e){return Y1(e)&&or(e.url)}function Y1(e){return!!e&&e[K1]}var x4=(e,t,n,r)=>P(i=>(new Df(t,i.targetRouterState,i.currentRouterState,n,r).activate(e),i)),Df=class{constructor(t,n,r,i,o){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Xd(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let i=ei(n);t.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else o&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=ei(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=ei(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let i=ei(n);t.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new gf(o.value.snapshot))}),t.children.length&&this.forwardEvent(new hf(t.value.snapshot))}activateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(Xd(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Xd(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Dl=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},ri=class{constructor(t,n){this.component=t,this.route=n}};function F4(e,t,n){let r=e._root,i=t?t._root:null;return no(r,i,n,[r.value])}function N4(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function ci(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!yp(e)?e:t.get(e):r}function no(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=ei(t);return e.children.forEach(s=>{R4(s,o[s.value.outlet],n,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>so(a,n.getContext(s),i)),i}function R4(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let l=O4(s,o,o.routeConfig.runGuardsAndResolvers);l?i.canActivateChecks.push(new Dl(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?no(e,t,a?a.children:null,r,i):no(e,t,n,r,i),l&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new ri(a.outlet.component,s))}else s&&so(t,a,i),i.canActivateChecks.push(new Dl(r)),o.component?no(e,null,a?a.children:null,r,i):no(e,null,n,r,i);return i}function O4(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!ir(e.url,t.url);case"pathParamsOrQueryParamsChange":return!ir(e.url,t.url)||!Rt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Cf(e,t)||!Rt(e.queryParams,t.queryParams);case"paramsChange":default:return!Cf(e,t)}}function so(e,t,n){let r=ei(e),i=e.value;Object.entries(r).forEach(([o,s])=>{i.component?t?so(s,t.children.getContext(o),n):so(s,null,n):so(s,t,n)}),i.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new ri(t.outlet.component,i)):n.canDeactivateChecks.push(new ri(null,i)):n.canDeactivateChecks.push(new ri(null,i))}function Co(e){return typeof e=="function"}function U4(e){return typeof e=="boolean"}function P4(e){return e&&Co(e.canLoad)}function k4(e){return e&&Co(e.canActivate)}function L4(e){return e&&Co(e.canActivateChild)}function V4(e){return e&&Co(e.canDeactivate)}function j4(e){return e&&Co(e.canMatch)}function X1(e){return e instanceof Pt||e?.name==="EmptyError"}var al=Symbol("INITIAL_VALUE");function li(){return Ve(e=>pi(e.map(t=>t.pipe(Lt(1),Ql(al)))).pipe(P(t=>{for(let n of t)if(n!==!0){if(n===al)return al;if(n===!1||B4(n))return n}return!0}),Le(t=>t!==al),Lt(1)))}function B4(e){return or(e)||e instanceof fo}function $4(e,t){return pe(n=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=n;return s.length===0&&o.length===0?F(Z(E({},n),{guardsResult:!0})):H4(s,r,i,e).pipe(pe(a=>a&&U4(a)?z4(r,o,e,t):F(a)),P(a=>Z(E({},n),{guardsResult:a})))})}function H4(e,t,n,r){return ae(e).pipe(pe(i=>K4(i.component,i.route,n,t,r)),Dt(i=>i!==!0,!0))}function z4(e,t,n,r){return ae(t).pipe(kt(i=>mr(W4(i.route.parent,r),G4(i.route,r),Z4(e,i.path,n),q4(e,i.route,n))),Dt(i=>i!==!0,!0))}function G4(e,t){return e!==null&&t&&t(new pf(e)),F(!0)}function W4(e,t){return e!==null&&t&&t(new ff(e)),F(!0)}function q4(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return F(!0);let i=r.map(o=>Xo(()=>{let s=yo(t)??n,a=ci(o,s),l=k4(a)?a.canActivate(t,e):qe(s,()=>a(t,e));return Fn(l).pipe(Dt())}));return F(i).pipe(li())}function Z4(e,t,n){let r=t[t.length-1],o=t.slice(0,t.length-1).reverse().map(s=>N4(s)).filter(s=>s!==null).map(s=>Xo(()=>{let a=s.guards.map(l=>{let c=yo(s.node)??n,u=ci(l,c),d=L4(u)?u.canActivateChild(r,e):qe(c,()=>u(r,e));return Fn(d).pipe(Dt())});return F(a).pipe(li())}));return F(o).pipe(li())}function K4(e,t,n,r,i){let o=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!o||o.length===0)return F(!0);let s=o.map(a=>{let l=yo(t)??i,c=ci(a,l),u=V4(c)?c.canDeactivate(e,t,n,r):qe(l,()=>c(e,t,n,r));return Fn(u).pipe(Dt())});return F(s).pipe(li())}function Q4(e,t,n,r){let i=t.canLoad;if(i===void 0||i.length===0)return F(!0);let o=i.map(s=>{let a=ci(s,e),l=P4(a)?a.canLoad(t,n):qe(e,()=>a(t,n));return Fn(l)});return F(o).pipe(li(),J1(r))}function J1(e){return Bl(Me(t=>{if(typeof t!="boolean")throw wl(e,t)}),P(t=>t===!0))}function Y4(e,t,n,r){let i=t.canMatch;if(!i||i.length===0)return F(!0);let o=i.map(s=>{let a=ci(s,e),l=j4(a)?a.canMatch(t,n):qe(e,()=>a(t,n));return Fn(l)});return F(o).pipe(li(),J1(r))}var ho=class{constructor(t){this.segmentGroup=t||null}},po=class extends Error{constructor(t){super(),this.urlTree=t}};function Jr(e){return pr(new ho(e))}function X4(e){return pr(new C(4e3,!1))}function J4(e){return pr(Q1(!1,Xe.GuardRejected))}var Ef=class{constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],i=n.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return F(r);if(i.numberOfChildren>1||!i.children[k])return X4(`${t.redirectTo}`);i=i.children[k]}}applyRedirectCommands(t,n,r,i,o){if(typeof n!="string"){let a=n,{queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,params:f,data:m,title:y}=i,D=qe(o,()=>a({params:f,data:m,queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,title:y}));if(D instanceof rn)throw new po(D);n=D}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new po(s);return s}applyRedirectCreateUrlTree(t,n,r,i){let o=this.createSegmentGroup(t,n.root,r,i);return new rn(o,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=n[a]}else r[i]=o}),r}createSegmentGroup(t,n,r,i){let o=this.createSegments(t,n.segments,r,i),s={};return Object.entries(n.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(t,l,r,i)}),new ee(o,s)}createSegments(t,n,r,i){return n.map(o=>o.path[0]===":"?this.findPosParam(t,o,i):this.findOrReturn(o,r))}findPosParam(t,n,r){let i=r[n.path.substring(1)];if(!i)throw new C(4001,!1);return i}findOrReturn(t,n){let r=0;for(let i of n){if(i.path===t.path)return n.splice(r),i;r++}return t}},bf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function e5(e,t,n,r,i){let o=Tf(e,t,n);return o.matched?(r=b4(t,r),Y4(r,t,n,i).pipe(P(s=>s===!0?o:E({},bf)))):F(o)}function Tf(e,t,n){if(t.path==="**")return t5(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?E({},bf):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let i=(t.matcher||Y_)(n,e,t);if(!i)return E({},bf);let o={};Object.entries(i.posParams??{}).forEach(([a,l])=>{o[a]=l.path});let s=i.consumed.length>0?E(E({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:n.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function t5(e){return{matched:!0,parameters:e.length>0?R1(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function S1(e,t,n,r){return n.length>0&&i5(e,n,r)?{segmentGroup:new ee(t,r5(r,new ee(n,e.children))),slicedSegments:[]}:n.length===0&&o5(e,n,r)?{segmentGroup:new ee(e.segments,n5(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new ee(e.segments,e.children),slicedSegments:n}}function n5(e,t,n,r){let i={};for(let o of n)if(Il(e,t,o)&&!r[Ct(o)]){let s=new ee([],{});i[Ct(o)]=s}return E(E({},r),i)}function r5(e,t){let n={};n[k]=t;for(let r of e)if(r.path===""&&Ct(r)!==k){let i=new ee([],{});n[Ct(r)]=i}return n}function i5(e,t,n){return n.some(r=>Il(e,t,r)&&Ct(r)!==k)}function o5(e,t,n){return n.some(r=>Il(e,t,r))}function Il(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function s5(e,t,n,r){return Ct(e)!==r&&(r===k||!Il(t,n,e))?!1:Tf(t,e,n).matched}function a5(e,t,n){return t.length===0&&!e.children[n]}var _f=class{};function l5(e,t,n,r,i,o,s="emptyOnly"){return new If(e,t,n,r,i,s,o).recognize()}var c5=31,If=class{constructor(t,n,r,i,o,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Ef(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new C(4002,`'${t.segmentGroup}'`)}recognize(){let t=S1(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(P(({children:n,rootSnapshot:r})=>{let i=new Ye(r,n),o=new Cl("",i),s=m4(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(t){let n=new ni([],Object.freeze({}),Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),k,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,k,n).pipe(P(r=>({children:r,rootSnapshot:n})),cn(r=>{if(r instanceof po)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof ho?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,o):this.processSegment(t,n,r,r.segments,i,!0,o).pipe(P(s=>s instanceof Ye?[s]:[]))}processChildren(t,n,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return ae(o).pipe(kt(s=>{let a=r.children[s],l=_4(n,s);return this.processSegmentGroup(t,l,a,s,i)}),Kl((s,a)=>(s.push(...a),s)),un(null),Zl(),pe(s=>{if(s===null)return Jr(r);let a=ey(s);return u5(a),F(a)}))}processSegment(t,n,r,i,o,s,a){return ae(n).pipe(kt(l=>this.processSegmentAgainstRoute(l._injector??t,n,l,r,i,o,s,a).pipe(cn(c=>{if(c instanceof ho)return F(null);throw c}))),Dt(l=>!!l),cn(l=>{if(X1(l))return a5(r,i,o)?F(new _f):Jr(r);throw l}))}processSegmentAgainstRoute(t,n,r,i,o,s,a,l){return s5(r,i,o,s)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,i,r,o,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,i,n,r,o,s,l):Jr(i):Jr(i)}expandSegmentAgainstRouteUsingRedirect(t,n,r,i,o,s,a){let{matched:l,parameters:c,consumedSegments:u,positionalParamSegments:d,remainingSegments:g}=Tf(n,i,o);if(!l)return Jr(n);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>c5&&(this.allowRedirects=!1));let f=new ni(o,c,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,A1(i),Ct(i),i.component??i._loadedComponent??null,i,T1(i)),m=vl(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(m.params),f.data=Object.freeze(m.data);let y=this.applyRedirects.applyRedirectCommands(u,i.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(i,y).pipe(pe(D=>this.processSegment(t,r,n,D.concat(g),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,i,o,s){let a=e5(n,r,i,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ve(l=>l.matched?(t=r._injector??t,this.getChildConfig(t,r,i).pipe(Ve(({routes:c})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:g,remainingSegments:f}=l,m=new ni(g,d,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,A1(r),Ct(r),r.component??r._loadedComponent??null,r,T1(r)),y=vl(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(y.params),m.data=Object.freeze(y.data);let{segmentGroup:D,slicedSegments:S}=S1(n,g,f,c);if(S.length===0&&D.hasChildren())return this.processChildren(u,c,D,m).pipe(P(A=>new Ye(m,A)));if(c.length===0&&S.length===0)return F(new Ye(m,[]));let H=Ct(r)===o;return this.processSegment(u,c,D,S,H?k:o,!0,m).pipe(P(A=>new Ye(m,A instanceof Ye?[A]:[])))}))):Jr(n)))}getChildConfig(t,n,r){return n.children?F({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?F({routes:n._loadedRoutes,injector:n._loadedInjector}):Q4(t,n,r,this.urlSerializer).pipe(pe(i=>i?this.configLoader.loadChildren(t,n).pipe(Me(o=>{n._loadedRoutes=o.routes,n._loadedInjector=o.injector})):J4(n))):F({routes:[],injector:t})}};function u5(e){e.sort((t,n)=>t.value.outlet===k?-1:n.value.outlet===k?1:t.value.outlet.localeCompare(n.value.outlet))}function d5(e){let t=e.value.routeConfig;return t&&t.path===""}function ey(e){let t=[],n=new Set;for(let r of e){if(!d5(r)){t.push(r);continue}let i=t.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),n.add(i)):t.push(r)}for(let r of n){let i=ey(r.children);t.push(new Ye(r.value,i))}return t.filter(r=>!n.has(r))}function A1(e){return e.data||{}}function T1(e){return e.resolve||{}}function f5(e,t,n,r,i,o){return pe(s=>l5(e,t,n,r,s.extractedUrl,i,o).pipe(P(({state:a,tree:l})=>Z(E({},s),{targetSnapshot:a,urlAfterRedirects:l}))))}function h5(e,t){return pe(n=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=n;if(!i.length)return F(n);let o=new Set(i.map(l=>l.route)),s=new Set;for(let l of o)if(!s.has(l))for(let c of ty(l))s.add(c);let a=0;return ae(s).pipe(kt(l=>o.has(l)?p5(l,r,e,t):(l.data=vl(l,l.parent,e).resolve,F(void 0))),Me(()=>a++),yr(1),pe(l=>a===s.size?F(n):Ge))})}function ty(e){let t=e.children.map(n=>ty(n)).flat();return[e,...t]}function p5(e,t,n,r){let i=e.routeConfig,o=e._resolve;return i?.title!==void 0&&!Z1(i)&&(o[go]=i.title),g5(o,e,t,r).pipe(P(s=>(e._resolvedData=s,e.data=vl(e,e.parent,n).resolve,null)))}function g5(e,t,n,r){let i=tf(e);if(i.length===0)return F({});let o={};return ae(i).pipe(pe(s=>m5(e[s],t,n,r).pipe(Dt(),Me(a=>{if(a instanceof fo)throw wl(new oi,a);o[s]=a}))),yr(1),ql(o),cn(s=>X1(s)?Ge:pr(s)))}function m5(e,t,n,r){let i=yo(t)??r,o=ci(e,i),s=o.resolve?o.resolve(t,n):qe(i,()=>o(t,n));return Fn(s)}function Jd(e){return Ve(t=>{let n=e(t);return n?ae(n).pipe(P(()=>t)):F(t)})}var ny=(()=>{class e{buildTitle(n){let r,i=n.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===k);return r}getResolvedTitleForRoute(n){return n.data[go]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(y5),providedIn:"root"})}}return e})(),y5=(()=>{class e extends ny{constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(_(I0))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),wo=new I("",{providedIn:"root",factory:()=>({})}),v5=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=ge({type:e,selectors:[["ng-component"]],standalone:!0,features:[Im],decls:1,vars:0,template:function(r,i){r&1&&v(0,"router-outlet")},dependencies:[Af],encapsulation:2})}}return e})();function xf(e){let t=e.children&&e.children.map(xf),n=t?Z(E({},e),{children:t}):E({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==k&&(n.component=v5),n}var El=new I(""),Ff=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=w(ua)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return F(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Fn(n.loadComponent()).pipe(P(ry),Me(o=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=o}),dn(()=>{this.componentLoaders.delete(n)})),i=new hr(r,()=>new Ie).pipe(fr());return this.componentLoaders.set(n,i),i}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return F({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=C5(r,this.compiler,n,this.onLoadEndListener).pipe(dn(()=>{this.childrenLoaders.delete(r)})),s=new hr(o,()=>new Ie).pipe(fr());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function C5(e,t,n,r){return Fn(e.loadChildren()).pipe(P(ry),pe(i=>i instanceof bi||Array.isArray(i)?F(i):ae(t.compileModuleAsync(i))),P(i=>{r&&r(e);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(n).injector,s=o.get(El,[],{optional:!0,self:!0}).flat()),{routes:s.map(xf),injector:o}}))}function w5(e){return e&&typeof e=="object"&&"default"in e}function ry(e){return w5(e)?e.default:e}var Nf=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(D5),providedIn:"root"})}}return e})(),D5=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),iy=new I(""),oy=new I("");function E5(e,t,n){let r=e.get(oy),i=e.get(Ee);return e.get(X).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(c=>setTimeout(c));let o,s=new Promise(c=>{o=c}),a=i.startViewTransition(()=>(o(),b5(e))),{onViewTransitionCreated:l}=r;return l&&qe(e,()=>l({transition:a,from:t,to:n})),s})}function b5(e){return new Promise(t=>{Fu({read:()=>setTimeout(t)},{injector:e})})}var _5=new I(""),Rf=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new Ie,this.transitionAbortSubject=new Ie,this.configLoader=w(Ff),this.environmentInjector=w(Oe),this.urlSerializer=w(mo),this.rootContexts=w(vo),this.location=w(Gr),this.inputBindingEnabled=w(_l,{optional:!0})!==null,this.titleStrategy=w(ny),this.options=w(wo,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=w(Nf),this.createViewTransition=w(iy,{optional:!0}),this.navigationErrorHandler=w(_5,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>F(void 0),this.rootComponentType=null;let n=i=>this.events.next(new uf(i)),r=i=>this.events.next(new df(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(Z(E(E({},this.transitions.value),n),{id:r}))}setupNavigations(n,r,i){return this.transitions=new Ae({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:oo,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Le(o=>o.id!==0),P(o=>Z(E({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),Ve(o=>{let s=!1,a=!1;return F(o).pipe(Ve(l=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Xe.SupersededByNewNavigation),Ge;this.currentTransition=o,this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,targetBrowserUrl:typeof l.extras.browserUrl=="string"?this.urlSerializer.parse(l.extras.browserUrl):l.extras.browserUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?Z(E({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=l.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!c&&u!=="reload"){let d="";return this.events.next(new Tn(l.id,this.urlSerializer.serialize(l.rawUrl),d,hl.IgnoredSameUrlNavigation)),l.resolve(!1),Ge}if(this.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return F(l).pipe(Ve(d=>{let g=this.transitions?.getValue();return this.events.next(new si(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),g!==this.transitions?.getValue()?Ge:Promise.resolve(d)}),f5(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Me(d=>{o.targetSnapshot=d.targetSnapshot,o.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=Z(E({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let g=new pl(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(g)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){let{id:d,extractedUrl:g,source:f,restoredState:m,extras:y}=l,D=new si(d,this.urlSerializer.serialize(g),f,m);this.events.next(D);let S=W1(this.rootComponentType).snapshot;return this.currentTransition=o=Z(E({},l),{targetSnapshot:S,urlAfterRedirects:g,extras:Z(E({},y),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,F(o)}else{let d="";return this.events.next(new Tn(l.id,this.urlSerializer.serialize(l.extractedUrl),d,hl.IgnoredByUrlHandlingStrategy)),l.resolve(!1),Ge}}),Me(l=>{let c=new sf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),P(l=>(this.currentTransition=o=Z(E({},l),{guards:F4(l.targetSnapshot,l.currentSnapshot,this.rootContexts)}),o)),$4(this.environmentInjector,l=>this.events.next(l)),Me(l=>{if(o.guardsResult=l.guardsResult,l.guardsResult&&typeof l.guardsResult!="boolean")throw wl(this.urlSerializer,l.guardsResult);let c=new af(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),Le(l=>l.guardsResult?!0:(this.cancelNavigationTransition(l,"",Xe.GuardRejected),!1)),Jd(l=>{if(l.guards.canActivateChecks.length)return F(l).pipe(Me(c=>{let u=new lf(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}),Ve(c=>{let u=!1;return F(c).pipe(h5(this.paramsInheritanceStrategy,this.environmentInjector),Me({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(c,"",Xe.NoDataFromResolver)}}))}),Me(c=>{let u=new cf(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}))}),Jd(l=>{let c=u=>{let d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe(Me(g=>{u.component=g}),P(()=>{})));for(let g of u.children)d.push(...c(g));return d};return pi(c(l.targetSnapshot.root)).pipe(un(null),Lt(1))}),Jd(()=>this.afterPreactivation()),Ve(()=>{let{currentSnapshot:l,targetSnapshot:c}=o,u=this.createViewTransition?.(this.environmentInjector,l.root,c.root);return u?ae(u).pipe(P(()=>o)):F(o)}),P(l=>{let c=M4(n.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=o=Z(E({},l),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,o}),Me(()=>{this.events.next(new co)}),x4(this.rootContexts,n.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),Lt(1),Me({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ot(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),this.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),Yl(this.transitionAbortSubject.pipe(Me(l=>{throw l}))),dn(()=>{!s&&!a&&this.cancelNavigationTransition(o,"",Xe.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),cn(l=>{if(a=!0,Y1(l))this.events.next(new nn(o.id,this.urlSerializer.serialize(o.extractedUrl),l.message,l.cancellationCode)),T4(l)?this.events.next(new ai(l.url,l.navigationBehaviorOptions)):o.resolve(!1);else{let c=new lo(o.id,this.urlSerializer.serialize(o.extractedUrl),l,o.targetSnapshot??void 0);try{let u=qe(this.environmentInjector,()=>this.navigationErrorHandler?.(c));if(u instanceof fo){let{message:d,cancellationCode:g}=wl(this.urlSerializer,u);this.events.next(new nn(o.id,this.urlSerializer.serialize(o.extractedUrl),d,g)),this.events.next(new ai(u.redirectTo,u.navigationBehaviorOptions))}else{this.events.next(c);let d=n.errorHandler(l);o.resolve(!!d)}}catch(u){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(u)}}return Ge}))}))}cancelNavigationTransition(n,r,i){let o=new nn(n.id,this.urlSerializer.serialize(n.extractedUrl),r,i);this.events.next(o),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function I5(e){return e!==oo}var M5=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(S5),providedIn:"root"})}}return e})(),Mf=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},S5=(()=>{class e extends Mf{static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=Ws(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),sy=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>w(A5),providedIn:"root"})}}return e})(),A5=(()=>{class e extends sy{constructor(){super(...arguments),this.location=w(Gr),this.urlSerializer=w(mo),this.options=w(wo,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=w(Nf),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new rn,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=W1(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof si)this.stateMemento=this.createStateMemento();else if(n instanceof Tn)this.rawUrlTree=r.initialUrl;else if(n instanceof pl){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else n instanceof co?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof nn&&(n.code===Xe.GuardRejected||n.code===Xe.NoDataFromResolver)?this.restoreHistory(r):n instanceof lo?this.restoreHistory(r,!0):n instanceof Ot&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let i=n instanceof rn?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let o=this.browserPageId,s=E(E({},r.extras.state),this.generateNgRouterState(r.id,o));this.location.replaceState(i,"",s)}else{let o=E(E({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",o)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.currentUrlTree===n.finalUrl&&o===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=Ws(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ro=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(ro||{});function ay(e,t){e.events.pipe(Le(n=>n instanceof Ot||n instanceof nn||n instanceof lo||n instanceof Tn),P(n=>n instanceof Ot||n instanceof Tn?ro.COMPLETE:(n instanceof nn?n.code===Xe.Redirect||n.code===Xe.SupersededByNewNavigation:!1)?ro.REDIRECTING:ro.FAILED),Le(n=>n!==ro.REDIRECTING),Lt(1)).subscribe(()=>{t()})}function T5(e){throw e}var x5={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},F5={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},xn=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=w(oa),this.stateManager=w(sy),this.options=w(wo,{optional:!0})||{},this.pendingTasks=w(Ht),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=w(Rf),this.urlSerializer=w(mo),this.location=w(Gr),this.urlHandlingStrategy=w(Nf),this._events=new Ie,this.errorHandler=this.options.errorHandler||T5,this.navigated=!1,this.routeReuseStrategy=w(M5),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=w(El,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!w(_l,{optional:!0}),this.eventsSubscription=new fe,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof nn&&r.code!==Xe.Redirect&&r.code!==Xe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ot)this.navigated=!0;else if(r instanceof ai){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),l=E({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||I5(i.source)},s);this.scheduleNavigation(a,oo,null,l,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}R5(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),oo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let l=E({},i);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(o.state=l)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(xf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s,u=null;switch(a){case"merge":u=E(E({},this.currentUrlTree.queryParams),o);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=o||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let g=i?i.snapshot:this.routerState.snapshot.root;d=$1(g)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return H1(d,n,u,c??null)}navigateByUrl(n,r={skipLocationChange:!1}){let i=or(n)?n:this.parseUrl(n),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,oo,null,r)}navigate(n,r={skipLocationChange:!1}){return N5(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let i;if(r===!0?i=E({},x5):r===!1?i=E({},F5):i=r,or(n))return E1(this.currentUrlTree,n,i);let o=this.parseUrl(n);return E1(this.currentUrlTree,o,i)}removeEmptyProps(n){return Object.entries(n).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(n,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((d,g)=>{a=d,l=g});let u=this.pendingTasks.add();return ay(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:o,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function N5(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new C(4008,!1)}function R5(e){return!(e instanceof co)&&!(e instanceof ai)}var ly=(()=>{class e{constructor(n,r,i,o,s,a){this.router=n,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new Ie,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let l=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=n.events.subscribe(c=>{c instanceof Ot&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(or(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,l),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:kg(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,n,r):i.removeAttribute(o,n)}get urlTree(){return this.routerLinkInput===null?null:or(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)($(xn),$(sr),gu("tabindex"),$(jr),$(zt),$(Xt))}}static{this.\u0275dir=gt({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Wt("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&ra("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Hr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Hr],replaceUrl:[2,"replaceUrl","replaceUrl",Hr],routerLink:"routerLink"},standalone:!0,features:[Ru,kr]})}}return e})();var bl=class{};var O5=(()=>{class e{constructor(n,r,i,o,s){this.router=n,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Le(n=>n instanceof Ot),kt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=na(o.providers,n,`Route: ${o.path}`));let s=o._injector??n,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return ae(i).pipe(gr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(n,r):i=F(null);let o=i.pipe(pe(s=>s===null?F(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ae([o,s]).pipe(gr())}else return o})}static{this.\u0275fac=function(r){return new(r||e)(_(xn),_(ua),_(Oe),_(bl),_(Ff))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),cy=new I(""),U5=(()=>{class e{constructor(n,r,i,o,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof si?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Ot?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Tn&&n.code===hl.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof gl&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new gl(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){Qg()}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function P5(e){return e.routerState.root}function Do(e,t){return{\u0275kind:e,\u0275providers:t}}function k5(){let e=w(We);return t=>{let n=e.get(En);if(t!==n.components[0])return;let r=e.get(xn),i=e.get(uy);e.get(Of)===1&&r.initialNavigation(),e.get(dy,null,V.Optional)?.setUpPreloading(),e.get(cy,null,V.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var uy=new I("",{factory:()=>new Ie}),Of=new I("",{providedIn:"root",factory:()=>1});function L5(){return Do(2,[{provide:Of,useValue:0},{provide:la,multi:!0,deps:[We],useFactory:t=>{let n=t.get(Zm,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let i=t.get(xn),o=t.get(uy);ay(i,()=>{r(!0)}),t.get(Rf).afterPreactivation=()=>(r(!0),o.closed?F(void 0):o),i.initialNavigation()}))}}])}function V5(){return Do(3,[{provide:la,multi:!0,useFactory:()=>{let t=w(xn);return()=>{t.setUpLocationChangeListener()}}},{provide:Of,useValue:2}])}var dy=new I("");function j5(e){return Do(0,[{provide:dy,useExisting:O5},{provide:bl,useExisting:e}])}function B5(){return Do(8,[M1,{provide:_l,useExisting:M1}])}function $5(e){let t=[{provide:iy,useValue:E5},{provide:oy,useValue:E({skipNextTransition:!!e?.skipInitialTransition},e)}];return Do(9,t)}var x1=new I("ROUTER_FORROOT_GUARD"),H5=[Gr,{provide:mo,useClass:oi},xn,vo,{provide:sr,useFactory:P5,deps:[xn]},Ff,[]],Uf=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[H5,[],{provide:El,multi:!0,useValue:n},{provide:x1,useFactory:q5,deps:[[xn,new js,new Jc]]},{provide:wo,useValue:r||{}},r?.useHash?G5():W5(),z5(),r?.preloadingStrategy?j5(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?Z5(r):[],r?.bindToComponentInputs?B5().\u0275providers:[],r?.enableViewTransitions?$5().\u0275providers:[],K5()]}}static forChild(n){return{ngModule:e,providers:[{provide:El,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(_(x1,8))}}static{this.\u0275mod=De({type:e})}static{this.\u0275inj=we({})}}return e})();function z5(){return{provide:cy,useFactory:()=>{let e=w(r0),t=w(X),n=w(wo),r=w(Rf),i=w(mo);return n.scrollOffset&&e.setOffset(n.scrollOffset),new U5(i,r,e,t,n)}}}function G5(){return{provide:Xt,useClass:Qm}}function W5(){return{provide:Xt,useClass:Qu}}function q5(e){return"guarded"}function Z5(e){return[e.initialNavigation==="disabled"?V5().\u0275providers:[],e.initialNavigation==="enabledBlocking"?L5().\u0275providers:[]]}var F1=new I("");function K5(){return[{provide:F1,useFactory:k5},{provide:ca,multi:!0,useExisting:F1}]}var hy=(()=>{class e{http;about={name:"",description:"",photo:"",email:"",portfolioUrl:""};constructor(n){this.http=n}ngOnInit(){this.getAboutData()}getAboutData(){this.http.get("https://portflio-backend-uiv7.onrender.com/api/about").subscribe(n=>{this.about=n},n=>{console.error("Error fetching About data:",n)})}static \u0275fac=function(r){return new(r||e)($(en))};static \u0275cmp=ge({type:e,selectors:[["app-about"]],decls:22,vars:1,consts:[["id","about-me",1,"py-12","lg:py-16"],[1,"max-w-1300","xl:max-w-1250","mx-auto","px-10","md:px-9","sm:px-8","xs:px-5"],[1,"relative","pb-0","lg:pb-25"],[1,"text-4xl","lg:text-5xl","xl:text-6xl","text-text-primary","font-bold","mb-5","font-pacifico"],[1,"flex","justify-between","gap-5","-mt-6","ml-5","sm:ml-10","xl:ml-32","2xl:ml-38","max-w-full","lg:flex-col","lg:justify-center","lg:items-center","lg:ml-0","lg:gap-12","lg:-mt-2"],[1,"relative","w-80","h-80","lg:w-96","lg:h-96","sm:w-72","sm:h-72"],[1,"absolute","bottom-0","-z-10","bg-accent-pink","w-4/5","h-4/5"],[1,"w-3/4","h-3/4","overflow-hidden","absolute","right-[15%]","top-[15%]","shadow-pink","transition-all","duration-200","ease-out","hover:scale-150","hover:rounded-3xl","hover:origin-top-center"],["src","images/roro3.jpg","alt","Roaa Ayman",1,"w-full","h-full","object-cover","scale-200","origin-center","transition-all","duration-200","ease-out","hover:scale-100"],[1,"flex","flex-col","items-center","gap-12","flex-none","basis-2/5","lg:flex-row-reverse"],["href","https://drive.google.com/file/d/1So0fhL4n2hvNY3CpCJktXAbe1ZYJg8be/view?usp=sharing","target","_blank",1,"group"],["type","button",1,"relative","mx-auto","py-3","px-4","transition-all","duration-200","ease-out","border-none","bg-transparent","cursor-pointer","before:content-['']","before:absolute","before:top-0","before:left-0","before:block","before:rounded-full","before:bg-primary-pink-lighter","before:w-11","before:h-11","before:transition-all","before:duration-300","before:ease-out","hover:before:w-full","active:scale-95"],[1,"relative","font-ubuntu","text-lg","font-bold","tracking-wider","text-text-secondary"],["width","15px","height","10px","viewBox","0 0 13 10",1,"relative","top-0","ml-2","fill-none","stroke-text-secondary","stroke-2","stroke-round","stroke-linejoin-round","-translate-x-1","transition-all","duration-300","ease-out","group-hover:translate-x-0"],["d","M1,5 L11,5"],["points","8 1 12 5 8 9"],[1,"text-text-secondary","font-semibold","text-lg","sm:text-base","mt-5"]],template:function(r,i){r&1&&(h(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3),N(4," About "),v(5,"br"),N(6," Roaa Ayman "),p(),h(7,"div",4)(8,"div",5),v(9,"div",6),h(10,"div",7),v(11,"img",8),p()(),h(12,"div",9)(13,"a",10)(14,"button",11)(15,"span",12),N(16,"Resume"),p(),Lr(),h(17,"svg",13),v(18,"path",14)(19,"polyline",15),p()()(),Vr(),h(20,"div",16),N(21),p()()()()()()),r&2&&(J(21),Dn(" ",i.about.description||"Loading description..."," "))},styles:['@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Pacifico;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6K6MmTpA.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Pacifico;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6D6MmTpA.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Pacifico;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6I6MmTpA.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Pacifico;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6J6MmTpA.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Pacifico;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6Mk.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1CzjvWyNL4U.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1CzjtGyNL4U.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1CzjvGyNL4U.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1Czjs2yNL4U.woff2) format("woff2");unicode-range:U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1CzjvmyNL4U.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoC1CzjsGyN.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKcg72j00.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKew72j00.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKcw72j00.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKfA72j00.woff2) format("woff2");unicode-range:U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKcQ72j00.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgoKfw72.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3jvWyNL4U.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3jtGyNL4U.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3jvGyNL4U.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3js2yNL4U.woff2) format("woff2");unicode-range:U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3jvmyNL4U.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3jsGyN.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjvWyNL4U.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjtGyNL4U.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjvGyNL4U.woff2) format("woff2");unicode-range:U+1F00-1FFF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjs2yNL4U.woff2) format("woff2");unicode-range:U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjvmyNL4U.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Ubuntu;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvjsGyN.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}']})}return e})();var py=(()=>{class e{elementRef;vantaEffect;originalSettings={speedLimit:9,separation:58,cohesion:40};hoverSettings={speedLimit:25,separation:250,cohesion:10};constructor(n){this.elementRef=n}ngOnInit(){this.vantaEffect=VANTA.BIRDS({el:"#vanta-birds",mouseControls:!0,touchControls:!0,gyroControls:!1,minHeight:200,minWidth:200,scale:1,scaleMobile:1,backgroundColor:11409308,color1:10687652,color2:15394322,birdSize:1.2,wingSpan:32,speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,alignment:77,cohesion:this.originalSettings.cohesion,quantity:4,backgroundAlpha:.35}),this.setupHoverEffects()}setupHoverEffects(){let n=this.elementRef.nativeElement.querySelector("#vanta-birds");n&&(n.addEventListener("mouseenter",()=>{this.onHoverStart()}),n.addEventListener("mouseleave",()=>{this.onHoverEnd()}))}onHoverStart(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.hoverSettings.speedLimit,separation:this.hoverSettings.separation,cohesion:this.hoverSettings.cohesion})}onHoverEnd(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,cohesion:this.originalSettings.cohesion})}ngOnDestroy(){this.vantaEffect&&this.vantaEffect.destroy()}static \u0275fac=function(r){return new(r||e)($(zt))};static \u0275cmp=ge({type:e,selectors:[["app-first"]],decls:7,vars:0,consts:[["id","vanta-birds",1,"hero-section"],[1,"profile-pic"],["src","images/roro.jpg","alt","Roaa Ayman"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"div",1),v(2,"img",2),p(),h(3,"h1"),N(4,"Roaa Ayman"),p(),h(5,"p"),N(6,"Recent Computer Science Graduate & Front-End Developer Specializing in Angular"),p()())},styles:[".hero-section[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;width:100vw;max-width:100%;color:var(--text-primary);overflow:hidden;cursor:pointer;transition:all .3s ease}.hero-section[_ngcontent-%COMP%]:hover{filter:brightness(1.05)}.profile-pic[_ngcontent-%COMP%]{border-radius:50%;overflow:hidden;width:150px;height:150px;margin-bottom:20px;border:4px solid var(--border-pink);box-shadow:0 0 20px var(--shadow-pink)}.profile-pic[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:10px;color:var(--text-primary);text-shadow:2px 2px 4px var(--shadow-pink)}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px;color:var(--text-secondary)}.social-icons[_ngcontent-%COMP%]{display:flex;gap:15px}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;transition:color .3s}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--accent-pink)}"]})}return e})();var Q5=[{path:"",component:py},{path:"about",component:hy},{path:"**",redirectTo:""}],gy=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=De({type:e});static \u0275inj=we({imports:[Uf.forRoot(Q5,{scrollPositionRestoration:"enabled",anchorScrolling:"enabled"}),Uf]})}return e})();var my=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=ge({type:e,selectors:[["app-header"]],decls:25,vars:0,consts:[[1,"h-[70px]","sticky","top-0","z-50","border-b-2","border-pink-300","theme-bg-gradient","px-8","flex","items-center","justify-between"],["type","checkbox","id","check",1,"hidden","peer"],["for","check",1,"menu","block","lg:hidden","cursor-pointer","z-50"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","currentColor",1,"bi","bi-list"],["fill-rule","evenodd","d","M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"],[1,"logo"],[1,"theme-text-primary","font-bold","cursor-pointer","text-xl"],[1,"nav-items","peer-checked:right-0","fixed","lg:static","top-0","right-[-250px]","h-screen","lg:h-auto","w-[250px]","lg:w-auto","flex","flex-col","lg:flex-row","justify-evenly","lg:justify-end","items-start","lg:items-center","bg-pink-400","lg:bg-transparent","transition-all","duration-500","p-8","lg:p-0","gap-y-6","lg:gap-x-6"],[1,"flex","flex-col","lg:flex-row","gap-y-4","lg:gap-x-4","theme-text-secondary","text-[18px]","font-medium"],["routerLink","/",1,"hover:text-pink-700","relative","after:block","after:h-[3px]","after:bg-pink-600","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["routerLink","/about",1,"hover:text-pink-700","relative","after:block","after:h-[3px]","after:bg-pink-600","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#skills",1,"hover:text-pink-700","relative","after:block","after:h-[3px]","after:bg-pink-600","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#projects",1,"hover:text-pink-700","relative","after:block","after:h-[3px]","after:bg-pink-600","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#contact",1,"hover:text-pink-700","relative","after:block","after:h-[3px]","after:bg-pink-600","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"]],template:function(r,i){r&1&&(h(0,"nav",0),v(1,"input",1),h(2,"label",2),Lr(),h(3,"svg",3),v(4,"path",4),p()(),Vr(),h(5,"div",5)(6,"h2",6),N(7,"RA"),p()(),h(8,"div",7)(9,"ul",8)(10,"li")(11,"a",9),N(12,"Home"),p()(),h(13,"li")(14,"a",10),N(15,"About"),p()(),h(16,"li")(17,"a",11),N(18,"Skills"),p()(),h(19,"li")(20,"a",12),N(21,"Projects"),p()(),h(22,"li")(23,"a",13),N(24,"Contact"),p()()()()())},dependencies:[ly],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}nav[_ngcontent-%COMP%]{height:70px;background:linear-gradient(to right,var(--primary-pink-lightest),var(--bg-gradient-middle),var(--rose-lighter));padding:0 2rem;display:flex;justify-content:space-between;align-items:center;top:0;z-index:1000;position:sticky;border-bottom:2px solid var(--border-pink-dark)}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:none}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:700;font-size:2rem;color:var(--text-primary);cursor:pointer;margin:0 .5rem;text-shadow:2px 2px 4px rgba(0,0,0,.3);transition:all .3s ease;position:relative;z-index:1}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:before{content:"";position:absolute;width:100%;height:2px;bottom:-5px;left:0;background:linear-gradient(to right,var(--primary-pink),var(--accent-pink));transform:scaleX(0);transform-origin:left;transition:transform .3s ease}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.nav-items[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{display:flex}.overview[_ngcontent-%COMP%]{margin-right:4rem}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:none}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style:none;margin:0 .5rem}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:var(--text-secondary);font-size:18px}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--primary-pink-dark)}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:"";display:block;height:3px;background:var(--primary-pink);width:0%;transition:all ease-in-out .3s}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after{width:100%}#check[_ngcontent-%COMP%], .menu[_ngcontent-%COMP%]{display:none}@media (max-width: 750px){.nav-items[_ngcontent-%COMP%]{z-index:1000;position:fixed;top:0;height:100vh;width:250px;flex-direction:column;justify-content:space-evenly;background:var(--accent-pink);padding:2rem;right:-250px;transition:all ease-in-out .5s}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{flex-direction:column;width:auto}.overview[_ngcontent-%COMP%]{margin:0}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:inline-block;font-weight:400;text-transform:uppercase;font-size:13px;margin-bottom:1rem}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:inline-block;cursor:pointer;vertical-align:top}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-block}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{margin-left:2px;transition:all ease-in-out .3s}.menu[_ngcontent-%COMP%]{display:inline-block;position:fixed;right:2.5rem;z-index:1001}#check[_ngcontent-%COMP%]:checked ~ .nav-items[_ngcontent-%COMP%]{right:0}}']})}return e})();var yy=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/projects";constructor(n){this.http=n}getProjects(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(_(en))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function J5(e,t){if(e&1&&(h(0,"span",19),N(1),p()),e&2){let n=t.$implicit;J(),Dn(" ",n," ")}}function eI(e,t){if(e&1){let n=Em();h(0,"div",5),Wt("mouseenter",function(){let i=su(n).index,o=qt();return au(o.onCardHover(i))})("mouseleave",function(){su(n);let i=qt();return au(i.onCardLeave())}),h(1,"div",6),v(2,"img",7)(3,"div",8),p(),h(4,"div",9)(5,"h3",10),N(6),p(),h(7,"p",11),N(8),p(),h(9,"p",12),N(10),p(),h(11,"div",13)(12,"strong",14),N(13,"Skills:"),p(),h(14,"div",15),ot(15,J5,2,1,"span",16),p()()(),h(16,"div",17)(17,"a",18),N(18," GitHub "),p(),h(19,"a",18),N(20," Live Demo "),p()()()}if(e&2){let n=t.$implicit,r=t.index,i=qt();me("@cardHover",i.getCardState(r))("@fadeInUp",void 0),J(2),me("src",n.photo,Zn),J(4),at(n.name),J(2),at(n.title),J(2),at(n.description),J(5),me("ngForOf",n.skills),J(2),me("href",n.githubLink,Zn),J(2),me("href",n.link,Zn)}}var vy=(()=>{class e{apiService;projects=[];hoveredCard=null;constructor(n){this.apiService=n}ngOnInit(){this.apiService.getProjects().subscribe(n=>{this.projects=n.map(r=>({photo:r.photo,name:r.name,title:r.title,description:r.description,link:r.link,githubLink:r.githubLink,skills:r.skills}))},n=>{console.error("Error fetching projects:",n)})}openGitHub(n){window.open(n,"_blank")}onCardHover(n){this.hoveredCard=n}onCardLeave(){this.hoveredCard=null}getCardState(n){return this.hoveredCard===n?"hovered":"normal"}static \u0275fac=function(r){return new(r||e)($(yy))};static \u0275cmp=ge({type:e,selectors:[["app-projects"]],decls:6,vars:3,consts:[[1,"text-center","mt-10"],[1,"text-5xl","font-righteous","uppercase","text-pink-600","mb-10","relative"],[1,"block","w-24","h-1","bg-pink-400","mt-2","mx-auto","rounded"],[1,"grid","grid-cols-1","sm:grid-cols-2","lg:grid-cols-3","gap-10","px-6"],["class","project-card bg-white border border-gray-200 rounded-2xl p-6 shadow-lg flex flex-col justify-between fade-in-up",3,"mouseenter","mouseleave",4,"ngFor","ngForOf"],[1,"project-card","bg-white","border","border-gray-200","rounded-2xl","p-6","shadow-lg","flex","flex-col","justify-between","fade-in-up",3,"mouseenter","mouseleave"],[1,"overflow-hidden","rounded-xl","mb-4","h-52","relative"],["alt","Project Image",1,"project-image","w-full","h-full","object-cover",3,"src"],[1,"absolute","inset-0","bg-gradient-to-t","from-pink-500/20","to-transparent","opacity-0","group-hover:opacity-100","transition-opacity","duration-500"],[1,"text-gray-800"],[1,"title-underline","text-2xl","font-bold","font-righteous","uppercase","text-gray-900"],[1,"text-sm","font-lato","uppercase","tracking-wide","text-pink-600","mt-1"],[1,"text-sm","mt-2","text-gray-700"],[1,"mt-4"],[1,"text-pink-600"],[1,"flex","flex-wrap","gap-2","mt-1"],["class","skill-tag bg-pink-100 text-pink-700 text-xs font-semibold px-2 py-1 rounded-full cursor-default",4,"ngFor","ngForOf"],[1,"mt-6","flex","justify-between","gap-3"],["target","_blank",1,"project-button","text-xs","uppercase","px-4","py-2","border","border-pink-500","text-pink-500","rounded-full","hover:bg-pink-500","hover:text-white","flex-1","text-center",3,"href"],[1,"skill-tag","bg-pink-100","text-pink-700","text-xs","font-semibold","px-2","py-1","rounded-full","cursor-default"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"h2",1),N(2," Projects "),v(3,"span",2),p()(),h(4,"div",3),ot(5,eI,21,9,"div",4),p()),r&2&&(me("@fadeInUp",void 0),J(4),me("@staggerAnimation",void 0),J(),me("ngForOf",i.projects))},dependencies:[Wr],styles:['.project-card[_ngcontent-%COMP%]{transition:all .5s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px) scale(1.02);box-shadow:0 25px 50px -12px #00000040,0 0 0 1px #ec48991a}.project-image[_ngcontent-%COMP%]{transition:transform .7s cubic-bezier(.4,0,.2,1);will-change:transform}.project-card[_ngcontent-%COMP%]:hover   .project-image[_ngcontent-%COMP%]{transform:scale(1.1)}.skill-tag[_ngcontent-%COMP%]{transition:all .2s cubic-bezier(.4,0,.2,1);will-change:transform}.skill-tag[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-1px);box-shadow:0 4px 8px #ec489933}.project-button[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.project-button[_ngcontent-%COMP%]:hover:before{left:100%}.project-button[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-2px);box-shadow:0 8px 16px #ec48994d}.title-underline[_ngcontent-%COMP%]{position:relative;overflow:hidden}.title-underline[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-2px;left:0;width:0;height:2px;background:linear-gradient(90deg,#ec4899,#f472b6);transition:width .3s ease-out}.project-card[_ngcontent-%COMP%]:hover   .title-underline[_ngcontent-%COMP%]:after{width:100%}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out forwards}.project-card[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.project-card[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.project-card[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.project-card[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.project-card[_ngcontent-%COMP%]:nth-child(5){animation-delay:.5s}.project-card[_ngcontent-%COMP%]:nth-child(6){animation-delay:.6s}'],data:{animation:[Ua("fadeInUp",[ka(":enter",[Ft({opacity:0,transform:"translateY(30px)"}),Pa("600ms ease-out",Ft({opacity:1,transform:"translateY(0)"}))])]),Ua("staggerAnimation",[ka("* => *",[A0(":enter",[Ft({opacity:0,transform:"translateY(30px)"}),T0(100,[Pa("600ms ease-out",Ft({opacity:1,transform:"translateY(0)"}))])],{optional:!0})])]),Ua("cardHover",[dd("normal",Ft({transform:"scale(1)"})),dd("hovered",Ft({transform:"scale(1.02)"})),ka("normal <=> hovered",Pa("300ms ease-in-out"))])]}})}return e})();var nI=(e,t)=>({success:e,error:t});function rI(e,t){if(e&1&&(h(0,"div",219),N(1),p()),e&2){let n=qt();me("ngClass",Mm(2,nI,n.submissionStatus==="success",n.submissionStatus==="error")),J(),Dn(" ",n.submissionMessage,`
`)}}var Cy=(()=>{class e{submissionStatus=null;submissionMessage="";onSubmit(n){n.preventDefault();let r=n.target;fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json"}}).then(i=>{i.ok?(this.submissionStatus="success",this.submissionMessage="Message sent successfully!",r.reset()):(this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later.")}).catch(()=>{this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later."})}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=ge({type:e,selectors:[["app-contact"]],decls:223,vars:1,consts:[[1,"contact-header"],[1,"heading"],[1,"container","d-flex","justify-content-center","align-items-center"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 790 563","fill","none"],["id","Image"],["id","g14"],["id","g16"],["id","g22"],["id","path24","d","M578.06 12.9772C592.384 8.33142 607.668 7.43103 622.682 8.31278C644.252 9.57946 666.668 15.0178 682.527 29.8837C692.521 39.2526 699.149 51.6277 707.182 62.7655C730.486 95.0785 766.513 118.198 782.236 154.912C795.674 186.289 790.623 225.749 767.498 250.666C744.37 275.583 703.649 282.658 675.018 264.535C647.531 247.136 635.383 212.503 610.273 191.742C592.326 176.901 569.144 170.28 549.646 157.607C529.69 144.636 513.457 124.248 509.79 100.515C506.745 80.8173 513.744 59.4156 528.903 46.4558C543.731 33.7796 559.331 19.0536 578.06 12.9772Z","fill","#D0F6FF"],["id","g26"],["id","path28","d","M702.629 254.14C677.841 258.169 653.602 251.674 628.841 247.05C605.059 242.608 581.372 234.267 562.49 218.522C553.842 211.31 546.177 202.344 542.784 191.529C537.944 176.097 542.362 159.436 542.319 143.243C542.267 124.241 537.593 105.929 524.57 91.9138C516.642 83.3826 507.429 75.9038 501.21 66.026C488.249 45.4368 498.285 17.8695 518.578 6.24557C537.067 -4.34208 560.588 -0.151769 579.793 9.03335C598.996 18.2198 615.855 31.9082 635.139 40.9228C656.28 50.8045 679.407 54.6779 702.724 56.9022C720.556 58.6044 738.716 56.5333 753.266 67.1156C763.675 74.6877 771.032 86.0519 775.307 98.2911C783.396 121.448 781.768 148.673 778.037 172.583C775.54 188.601 770.517 204.461 761.348 217.755C750.094 234.074 732.89 245.819 714.058 251.504C710.234 252.66 706.426 253.523 702.629 254.14Z","fill","#ADE0EC"],["id","g30"],["id","path32","d","M663.601 562.578H87.0689C43.5385 528.913 13.2922 480.886 5.1219 426.023C1.72497 403.207 3.65744 376.191 22.008 362.528C50.2285 341.516 92.5784 368.009 124.46 353.325C144.998 343.869 155.119 319.297 155.332 296.439C155.544 273.583 147.922 251.523 142.217 229.409C136.51 207.295 132.749 183.417 140.459 161.935C148.169 140.454 170.87 123.025 192.716 128.727C211.437 133.614 223.318 152.833 241.257 160.133C259.931 167.732 281.608 160.819 298.184 149.256C314.758 137.694 327.949 121.87 343.441 108.858C370.638 86.0156 406.562 72.0169 441.495 77.35C476.426 82.6831 508.747 110.108 514.202 145.471C518.662 174.4 506.652 207.826 524.152 231.129C543.883 257.401 585.152 250.025 613.676 265.983C636.899 278.972 649.286 309.077 642.052 334.934C634.666 361.336 609.565 383.494 613.653 410.622C616.583 430.071 633.6 443.505 645.587 458.982C668.627 488.727 679.049 528.158 663.601 562.578Z","fill","#D0F6FF"],["id","g34"],["id","path36","d","M636.536 562.578H142.588C127.567 548.706 110.711 535.931 102.179 517.242C93.6475 498.553 93.6698 474.269 107.702 459.372C124.638 441.394 152.947 443.847 176.763 437.899C204.228 431.038 229.205 408.689 232.723 380.251C237.265 343.537 206.911 309.992 208.804 273.041C210.296 243.911 234.698 217.737 263.314 214.567C282.66 212.424 302.727 219.607 321.415 214.109C338.741 209.012 351.237 194.119 366.296 184.052C383.968 172.235 406.528 167.099 426.891 172.974C447.257 178.85 464.492 196.695 467.235 217.968C470.152 240.588 458.004 266.283 470.888 284.991C480.485 298.927 499.63 301.618 516.392 301.075C533.155 300.531 551.03 298.252 565.763 306.372C579.463 313.921 587.611 329.548 589.138 345.273C590.664 360.996 586.334 376.788 579.943 391.199C574.357 403.794 567.162 415.706 562.961 428.843C558.759 441.979 557.893 457.066 564.737 469.006C571.941 481.577 585.915 488.105 597.307 496.94C617.442 512.552 635.027 536.936 636.536 562.578Z","fill","#ADE0EC"],["id","g38"],["id","path40","d","M595.195 76.2172L623.725 149.709L684.511 114.948L595.195 76.2172Z","fill","#FAFAFA"],["id","g42"],["id","path44","d","M595.195 76.2172L651.26 133.962L666.528 125.232L595.195 76.2172Z","fill","#DADADA"],["id","g46"],["id","path48","d","M666.528 125.231L655.896 151.885L651.262 133.962L666.528 125.231Z","fill","#DADADA"],["id","g50"],["id","path52","d","M655.896 151.885L642.776 138.814L651.262 133.962L655.896 151.885Z","fill","#B2B2B2"],["id","g54"],["id","path56","d","M222.015 539.778C157.683 522.604 101.579 476.087 72.2367 415.592C60.1279 390.628 52.3612 362.908 54.182 335.155C56.0014 307.4 68.2732 279.663 90.2639 263.011C112.253 246.359 144.303 242.756 167.56 257.538C190.03 271.821 200.733 299.209 220.204 317.461C243.475 339.274 280.404 345.641 308.459 330.683C336.514 315.723 352.288 279.369 342.05 248.968C332.575 220.834 305.793 203.339 282.527 185.228C259.261 167.115 236.126 141.651 239.454 112.116C242.315 86.7319 264.382 67.653 287.628 57.7513C332.132 38.7951 389.516 47.2223 419.844 85.2787C452.476 126.224 446.202 185.954 431.486 236.425C416.769 286.896 395.069 337.985 402.391 390.086C408.475 433.375 434.97 472.304 470.109 497.688C505.247 523.075 548.365 535.649 591.441 538.326C634.426 540.999 680.569 532.908 712.364 503.476C744.158 474.044 754.899 419.157 726.78 386.108C712.226 369.003 690.497 360.328 669.604 352.466C648.708 344.604 626.907 336.377 611.765 319.807C596.621 303.236 590.753 275.553 604.995 258.181C621.492 238.058 665.44 235.858 680.982 214.969C692.069 200.069 679.116 171.364 666.529 157.269","stroke","#00C0E0","stroke-width","2.541","stroke-miterlimit","10","stroke-dasharray","7.62 7.62"],["id","g58"],["id","path60","d","M186.221 462.671C158.423 444.172 133.639 421.035 113.173 394.475C104.595 383.341 96.7115 371.5 91.5083 358.398C86.3038 345.294 83.8862 330.794 86.4431 316.909C88.2757 306.953 93.6209 296.589 103.112 293.404C110.525 290.917 118.902 293.505 125.077 298.35C131.253 303.195 135.584 310.023 139.418 316.916C154.207 343.52 163.287 372.9 174.224 401.352C179.474 415.006 185.205 428.511 192.17 441.366C195.631 447.754 199.387 453.984 203.532 459.939C207.289 465.334 214.117 471.144 216.477 476.969C211.073 481.321 191.263 466.026 186.221 462.671Z","fill","#009D9C"],["id","g62"],["id","path64","d","M107.952 308.508C121.544 366.877 153.477 420.713 197.968 460.267","stroke","#00BBBF","stroke-width","2.02","stroke-miterlimit","10"],["id","g66"],["id","path68","d","M556.282 462.962C580.155 451.221 602.114 435.493 621.004 416.609C628.922 408.693 636.362 400.145 641.81 390.319C647.257 380.493 650.64 369.27 650.028 358.018C649.587 349.946 646.41 341.19 639.223 337.682C633.608 334.942 626.717 336.117 621.339 339.307C615.961 342.497 611.841 347.447 608.109 352.504C593.705 372.014 583.539 394.316 571.997 415.691C566.459 425.947 560.553 436.037 553.736 445.484C550.349 450.177 546.746 454.716 542.861 458.995C539.341 462.875 533.349 466.761 530.891 471.124C534.727 475.129 551.952 465.092 556.282 462.962Z","fill","#009D9C"],["id","g70"],["id","path72","d","M633.861 349.129C617.182 393.899 586.452 433.173 547.233 459.836","stroke","#00BBBF","stroke-width","1.612","stroke-miterlimit","10"],["id","g74"],["id","path76","d","M198.233 424.458C213.177 349.774 197.247 269.251 155.048 206.17","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g78"],["id","path80","d","M159.471 213.554C147.424 209.56 136.887 201.07 130.331 190.079C123.775 179.087 121.256 165.687 123.366 153.024C136.148 156.495 148.154 164.541 154.962 176.037C161.771 187.536 162.465 200.493 159.471 213.554Z","fill","#11ABBA"],["id","g82"],["id","path84","d","M172.923 237.731C170.163 228.217 170.886 217.71 174.922 208.676C178.958 199.643 186.273 192.157 195.149 187.981C198.557 197.74 198.756 208.999 194.512 218.417C190.269 227.834 182.434 233.949 172.923 237.731Z","fill","#11ABBA"],["id","g86"],["id","path88","d","M173.775 236.831C166.404 230.308 156.684 226.574 146.897 226.504C137.11 226.434 127.338 230.03 119.876 236.447C127.196 243.672 137.206 248.568 147.423 248.608C157.641 248.647 166.403 243.999 173.775 236.831Z","fill","#11ABBA"],["id","g90"],["id","path92","d","M188.104 276.094C187.024 266.239 189.542 256.02 195.07 247.837C200.597 239.655 209.088 233.576 218.546 231.029C220.225 241.241 218.483 252.363 212.686 260.887C206.887 269.41 198.122 274.049 188.104 276.094Z","fill","#11ABBA"],["id","g94"],["id","path96","d","M189.099 275.358C182.962 267.634 174.033 262.24 164.408 260.443C154.782 258.647 144.542 260.463 136.091 265.464C142.057 273.87 151.07 280.459 161.124 282.301C171.179 284.145 180.606 281.115 189.099 275.358Z","fill","#11ABBA"],["id","g98"],["id","path100","d","M198.154 314.469C197.924 304.556 201.31 294.598 207.521 286.933C213.729 279.267 222.71 273.961 232.351 272.257C233.146 282.578 230.456 293.504 223.948 301.485C217.439 309.467 208.308 313.315 198.154 314.469Z","fill","#11ABBA"],["id","g102"],["id","path104","d","M199.208 313.823C193.758 305.586 185.324 299.426 175.891 296.789C166.457 294.15 156.099 295.057 147.252 299.294C152.471 308.194 160.885 315.553 170.744 318.274C180.602 320.997 190.253 318.808 199.208 313.823Z","fill","#11ABBA"],["id","g106"],["id","path108","d","M203.971 356.696C205.264 346.866 210.136 337.563 217.445 330.968C224.754 324.372 234.439 320.543 244.225 320.378C243.428 330.699 239.095 341.071 231.443 347.929C223.789 354.789 214.179 357.154 203.971 356.696Z","fill","#11ABBA"],["id","g110"],["id","path112","d","M205.112 356.224C200.99 347.23 193.605 339.817 184.689 335.725C175.775 331.635 165.404 330.9 156.012 333.694C159.806 343.307 166.988 351.901 176.31 356.142C185.632 360.381 195.5 359.74 205.112 356.224Z","fill","#11ABBA"],["id","g114"],["id","path116","d","M546.285 450.207C530.11 375.786 544.71 295.004 585.861 231.219","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g118"],["id","path120","d","M581.562 238.676C593.54 234.478 603.937 225.811 610.312 214.71C616.685 203.608 618.983 190.168 616.663 177.542C603.94 181.23 592.069 189.476 585.452 201.088C578.835 212.7 578.354 225.668 581.562 238.676Z","fill","#11ABBA"],["id","g122"],["id","path124","d","M568.512 263.078C571.114 253.518 570.219 243.024 566.033 234.06C561.85 225.096 554.412 217.737 545.469 213.71C542.22 223.525 542.208 234.787 546.607 244.131C551.006 253.476 558.939 259.457 568.512 263.078Z","fill","#11ABBA"],["id","g126"],["id","path128","d","M567.646 262.192C574.907 255.545 584.566 251.647 594.349 251.411C604.134 251.175 613.963 254.605 621.528 260.895C614.331 268.242 604.403 273.308 594.187 273.52C583.972 273.732 575.135 269.234 567.646 262.192Z","fill","#11ABBA"],["id","g130"],["id","path132","d","M553.965 301.692C554.883 291.82 552.196 281.645 546.534 273.556C540.872 265.469 532.283 259.535 522.783 257.148C521.274 267.388 523.198 278.478 529.136 286.902C535.074 295.328 543.915 299.817 553.965 301.692Z","fill","#11ABBA"],["id","g134"],["id","path136","d","M552.959 300.973C558.968 293.147 567.807 287.6 577.401 285.642C586.995 283.683 597.263 285.324 605.795 290.182C599.97 298.687 591.066 305.428 581.044 307.441C571.021 309.454 561.546 306.585 552.959 300.973Z","fill","#11ABBA"],["id","g138"],["id","path140","d","M544.55 340.232C544.617 330.317 541.066 320.416 534.731 312.857C528.396 305.299 519.329 300.144 509.661 298.606C509.036 308.939 511.905 319.818 518.546 327.687C525.186 335.556 534.379 339.25 544.55 340.232Z","fill","#11ABBA"],["id","g142"],["id","path144","d","M543.486 339.603C548.799 331.276 557.13 324.975 566.519 322.176C575.908 319.378 586.279 320.109 595.196 324.198C590.124 333.185 581.833 340.685 572.021 343.571C562.207 346.46 552.522 344.437 543.486 339.603Z","fill","#11ABBA"],["id","g146"],["id","path148","d","M539.431 382.551C537.978 372.745 532.951 363.525 525.535 357.055C518.117 350.586 508.371 346.92 498.585 346.921C499.551 357.227 504.053 367.523 511.819 374.253C519.583 380.981 529.232 383.182 539.431 382.551Z","fill","#11ABBA"],["id","g150"],["id","path152","d","M538.282 382.098C542.255 373.036 549.518 365.498 558.363 361.259C567.21 357.016 577.568 356.105 587.003 358.74C583.369 368.417 576.328 377.13 567.079 381.527C557.828 385.925 547.95 385.452 538.282 382.098Z","fill","#11ABBA"],["id","g154"],["id","path156","d","M186.615 500.321C190.696 492.791 196.119 485.823 199.682 478.076C190.178 465.849 178.777 454.862 166.819 445.23C159.004 438.931 150.847 433.032 142.419 427.531C134.688 433.762 126.957 439.994 119.225 446.225C120.579 435.351 121.356 425.888 122.482 415.574C105.313 406.143 87.2411 398.331 68.6211 392.377C64.3289 399.386 60.6691 406.825 54.8967 412.829C54.9847 404.798 54.2249 396.412 53.1469 387.893C35.9349 383.405 18.3639 380.482 0.707452 379.308C0.649609 386.531 1.06635 393.746 1.88798 400.912C6.50223 399.507 10.074 395.563 14.9604 394.821C11.7383 402.728 8.82513 411.421 4.99044 419.449C9.19717 438.521 16.3959 456.93 26.2186 473.763C34.3468 468.915 41.9636 462.248 51.7627 458.125C50.0576 473.301 50.0274 489.179 48.7351 504.527C53.8963 510.215 59.4097 515.573 65.2741 520.527C75.5977 529.245 86.9217 536.691 98.9201 542.791C101.353 533.385 103.872 524.016 109.898 516.114C116.996 529.781 124.688 541.96 131.128 555.467C157.986 563.194 186.571 564.779 214.002 559.454C218.542 558.574 222.349 551.211 223.76 546.749C225.172 542.289 224.898 537.468 224.262 532.827C222.26 518.237 216.907 504.646 209.377 492.145C201.36 494.069 193.248 496.332 186.615 500.321Z","fill","#11ABBA"],["id","g158"],["id","path160","d","M194.298 545.299C131.158 507.676 73.43 460.749 23.4922 406.451","stroke","#55CDE2","stroke-width","2.541","stroke-miterlimit","10"],["id","g162"],["id","path164","d","M559.699 515.384C555.868 510.221 551.098 505.622 547.63 500.242C553.415 490.113 560.744 480.704 568.626 472.241C573.781 466.709 579.23 461.436 584.922 456.429C591.334 460.232 597.744 464.032 604.155 467.835C602.002 459.887 600.425 452.929 598.502 445.374C610.285 436.498 622.913 428.73 636.143 422.286C640.078 427.037 643.584 432.176 648.514 436.023C647.601 430.055 647.283 423.73 647.188 417.273C659.526 412.073 672.295 407.997 685.312 405.212C686.116 410.582 686.566 415.998 686.708 421.42C683.127 420.873 680.053 418.324 676.338 418.3C679.571 423.837 682.654 429.991 686.353 435.55C685.232 450.201 681.815 464.681 676.277 478.269C669.715 475.541 663.346 471.403 655.618 469.394C658.486 480.504 660.182 492.319 662.759 503.602C659.518 508.393 655.978 512.977 652.135 517.298C645.372 524.903 637.727 531.67 629.441 537.508C626.639 530.772 623.778 524.07 618.459 518.842C614.616 529.781 610.176 539.676 606.805 550.427C587.63 559.082 566.522 563.353 545.546 562.358C542.075 562.193 538.466 557.123 536.945 553.957C535.425 550.79 535.121 547.171 535.105 543.651C535.058 532.573 537.61 521.88 541.897 511.761C548.065 512.326 554.342 513.133 559.699 515.384Z","fill","#11ABBA"],["id","g166"],["id","path168","d","M558.719 549.691C601.746 514.86 639.767 473.689 671.212 427.878","stroke","#55CDE2","stroke-width","1.91","stroke-miterlimit","10"],["id","g170"],["id","path172","d","M554.113 562.578H187.856C180.008 562.578 173.645 556.132 173.645 548.18V310.114C173.645 302.163 180.008 295.717 187.856 295.717H554.113C561.963 295.717 568.324 302.163 568.324 310.114V548.18C568.324 556.132 561.963 562.578 554.113 562.578Z","fill","#060C37"],["id","g174"],["id","path176","d","M563.719 429.147C563.719 435.866 558.342 441.314 551.71 441.314C545.078 441.314 539.701 435.866 539.701 429.147C539.701 422.427 545.078 416.981 551.71 416.981C558.342 416.981 563.719 422.427 563.719 429.147Z","fill","#111E65"],["id","g178"],["id","path180","d","M182.05 474.266C179.95 474.266 178.247 472.542 178.247 470.413V387.882C178.247 385.753 179.95 384.028 182.05 384.028C184.151 384.028 185.854 385.753 185.854 387.882V470.413C185.854 472.542 184.151 474.266 182.05 474.266Z","fill","#111E65"],["id","path182","d","M535.104 552.722H191.254V305.564H535.104V552.722Z","fill","#D8E9F5"],["id","path184","d","M535.1 322.18H191.256V305.568H535.1V322.18Z","fill","#A4B1BA"],["id","path186","d","M201.252 320.17H196.898V314.56H201.252V320.17Z","fill","#FF6044"],["id","path188","d","M206.906 320.17H202.552V310.653H206.906V320.17Z","fill","#FF6044"],["id","path190","d","M212.886 320.17H208.532V307.952H212.886V320.17Z","fill","#FF6044"],["id","g192"],["id","path194","d","M507.781 308.957V309.767C507.781 310.411 507.264 310.933 506.629 310.933H505.346C504.711 310.933 504.196 311.455 504.196 312.099V315.647C504.196 316.293 504.711 316.814 505.346 316.814H506.629C507.264 316.814 507.781 317.336 507.781 317.979V318.792C507.781 319.435 508.296 319.957 508.931 319.957H526.844C527.479 319.957 527.995 319.435 527.995 318.792V308.957C527.995 308.313 527.479 307.791 526.844 307.791H508.931C508.296 307.791 507.781 308.313 507.781 308.957Z","fill","#D8E9F5"],["id","g196"],["id","path198","d","M526.894 319.341H523.692C523.458 319.341 523.267 319.148 523.267 318.909V308.824C523.267 308.584 523.458 308.391 523.692 308.391H526.894C527.13 308.391 527.32 308.584 527.32 308.824V318.909C527.32 319.148 527.13 319.341 526.894 319.341Z","fill","#92FC28"],["id","g200"],["id","path202","d","M521.94 319.341H518.739C518.505 319.341 518.313 319.148 518.313 318.909V308.824C518.313 308.584 518.505 308.391 518.739 308.391H521.94C522.175 308.391 522.366 308.584 522.366 308.824V318.909C522.366 319.148 522.175 319.341 521.94 319.341Z","fill","#92FC28"],["id","g204"],["id","path206","d","M516.987 319.341H513.785C513.551 319.341 513.36 319.148 513.36 318.909V308.824C513.36 308.584 513.551 308.391 513.785 308.391H516.987C517.223 308.391 517.413 308.584 517.413 308.824V318.909C517.413 319.148 517.223 319.341 516.987 319.341Z","fill","#92FC28"],["id","g208"],["id","path210","d","M498.8 313.874C498.8 316.456 496.733 318.551 494.183 318.551C491.635 318.551 489.569 316.456 489.569 313.874C489.569 311.292 491.635 309.197 494.183 309.197C496.733 309.197 498.8 311.292 498.8 313.874Z","fill","#D8E9F5"],["id","path212","d","M513.36 533.681H212.999V340.836H513.36V533.681Z","fill","#C0CFDA"],["id","path214","d","M513.36 357.464H212.999V340.838H513.36V357.464Z","fill","#A4B3BC"],["id","path216","d","M507.28 373.991H310.642V366.083H507.28V373.991Z","fill","#DCEEFB"],["id","path218","d","M419.169 389.046H310.642V381.138H419.169V389.046Z","fill","#DCEEFB"],["id","path220","d","M369.032 404.104H310.642V396.196H369.032V404.104Z","fill","#DCEEFB"],["id","path222","d","M507.28 430.213H310.642V422.305H507.28V430.213Z","fill","#DCEEFB"],["id","path224","d","M419.169 445.268H310.642V437.36H419.169V445.268Z","fill","#DCEEFB"],["id","path226","d","M369.032 460.325H310.642V452.418H369.032V460.325Z","fill","#DCEEFB"],["id","path228","d","M507.28 485.114H310.642V477.206H507.28V485.114Z","fill","#DCEEFB"],["id","path230","d","M419.169 500.172H310.642V492.264H419.169V500.172Z","fill","#DCEEFB"],["id","path232","d","M369.032 515.228H310.642V507.32H369.032V515.228Z","fill","#DCEEFB"],["id","path234","d","M301.035 409.578H224.781V366.082H301.035V409.578Z","fill","#DCEEFB"],["id","g236"],["id","path238","d","M224.781 409.579L262.908 387.831L301.034 409.579H224.781Z","fill","#CADBE7"],["id","g240"],["id","path242","d","M301.034 366.082L262.908 387.83L224.781 366.082H301.034Z","fill","#CADBE7"],["id","path244","d","M301.035 465.546H224.781V422.05H301.035V465.546Z","fill","#DCEEFB"],["id","g246"],["id","path248","d","M224.781 465.546L262.908 443.798L301.034 465.546H224.781Z","fill","#CADBE7"],["id","g250"],["id","path252","d","M301.034 422.05L262.908 443.798L224.781 422.05H301.034Z","fill","#CADBE7"],["id","path254","d","M301.035 521.515H224.781V478.019H301.035V521.515Z","fill","#DCEEFB"],["id","g256"],["id","path258","d","M224.781 521.514L262.908 499.766L301.034 521.514H224.781Z","fill","#CADBE7"],["id","g260"],["id","path262","d","M301.034 478.018L262.908 499.766L224.781 478.018H301.034Z","fill","#CADBE7"],["id","g264"],["id","g282"],["id","g280","opacity","0.440002"],["id","g274","opacity","0.440002"],["id","path272","opacity","0.440002","d","M314.124 305.565L191.254 430.069V321.271L206.769 305.565H314.124Z","fill","white"],["id","g278","opacity","0.440002"],["id","path276","opacity","0.440002","d","M388.697 305.565L191.254 505.613V449.961L333.77 305.565H388.697Z","fill","white"],["id","g284"],["id","g302"],["id","g300","opacity","0.440002"],["id","g294","opacity","0.440002"],["id","path292","opacity","0.440002","d","M535.104 332.465V441.249L425.071 552.723H317.715L535.104 332.465Z","fill","white"],["id","g298","opacity","0.440002"],["id","path296","opacity","0.440002","d","M535.104 461.142V516.794L499.632 552.723H444.716L535.104 461.142Z","fill","white"],["id","envelope"],["id","g304"],["id","path306","d","M249.266 298.798L351.208 218.764C357.652 213.705 366.657 213.705 373.102 218.764L475.045 298.798V432.924H249.266V298.798Z","fill","#FF9004"],["id","path308","d","M448.926 227.706H275.382V421.076H448.926V227.706Z","fill","#FAFAFA"],["id","path310","d","M438.481 239.346H285.831V245.241H438.481V239.346Z","fill","#DCDCDC"],["id","path312","d","M415.561 251.195H285.831V257.09H415.561V251.195Z","fill","#DCDCDC"],["id","path314","d","M394.51 263.044H285.831V268.939H394.51V263.044Z","fill","#DCDCDC"],["id","path316","d","M394.51 285.792H285.831V291.688H394.51V285.792Z","fill","#DCDCDC"],["id","path318","d","M366.443 297.167H285.831V303.062H366.443V297.167Z","fill","#DCDCDC"],["id","path320","d","M442.769 321H362.156V326.896H442.769V321Z","fill","#DCDCDC"],["id","path322","d","M442.768 332.609H377.201V338.504H442.768V332.609Z","fill","#DCDCDC"],["id","g324"],["id","path326","d","M362.155 365.9L249.265 298.877V432.924L362.155 365.9Z","fill","#FFAE35"],["id","g328"],["id","path330","d","M362.156 365.9L475.045 298.877V432.924L362.156 365.9Z","fill","#FFAE35"],["id","g332"],["id","path334","d","M351.209 352.89L249.267 432.924H475.044L373.102 352.89C366.658 347.831 357.652 347.831 351.209 352.89Z","fill","#FFBF69"],["id","g348"],["id","path350","d","M185.705 159.357C185.994 158.402 185.854 157.315 185.28 156.095C184.719 154.898 183.98 154.112 183.067 153.736C182.152 153.361 181.213 153.405 180.251 153.868C179.287 154.333 178.667 155.04 178.388 155.99C178.109 156.941 178.251 158.015 178.813 159.212C179.375 160.409 180.11 161.203 181.02 161.595C181.927 161.986 182.863 161.951 183.826 161.487C184.789 161.022 185.415 160.312 185.705 159.357ZM184.018 139.899C186.987 140.019 189.648 140.858 192.003 142.415C194.358 143.972 196.169 146.103 197.439 148.81C198.376 150.805 198.868 152.668 198.915 154.398C198.964 156.13 198.62 157.627 197.886 158.892C197.151 160.158 196.083 161.127 194.682 161.803C193.522 162.361 192.412 162.597 191.351 162.51C190.29 162.423 189.34 161.997 188.499 161.234C188.332 163.679 187.01 165.499 184.538 166.691C183.247 167.313 181.88 167.543 180.435 167.382C178.991 167.222 177.639 166.671 176.378 165.728C175.116 164.786 174.101 163.494 173.331 161.853C172.56 160.212 172.207 158.602 172.27 157.021C172.334 155.441 172.761 154.037 173.555 152.812C174.35 151.588 175.402 150.658 176.716 150.026C178.642 149.097 180.458 148.996 182.169 149.723L181.404 148.093L186.755 145.514L191.517 155.66C191.851 156.368 192.222 156.816 192.63 157C193.038 157.183 193.46 157.17 193.898 156.96C195.278 156.295 195.16 154.244 193.547 150.807C192.558 148.7 191.191 147.062 189.448 145.89C187.703 144.718 185.729 144.098 183.524 144.033C181.32 143.967 179.056 144.493 176.737 145.61C174.438 146.718 172.631 148.201 171.317 150.058C170.001 151.916 169.265 153.963 169.108 156.2C168.949 158.438 169.386 160.655 170.416 162.85C171.468 165.09 172.892 166.864 174.687 168.175C176.483 169.485 178.493 170.207 180.719 170.346C182.943 170.483 185.205 169.998 187.503 168.891C189.845 167.763 191.793 166.226 193.349 164.28L196.235 167.254C195.479 168.216 194.473 169.176 193.219 170.134C191.964 171.092 190.636 171.908 189.237 172.583C186.063 174.113 182.955 174.794 179.912 174.629C176.868 174.464 174.138 173.537 171.722 171.846C169.304 170.157 167.414 167.859 166.05 164.954C164.698 162.072 164.144 159.149 164.393 156.189C164.639 153.228 165.671 150.494 167.487 147.988C169.301 145.481 171.807 143.458 175.003 141.918C178.045 140.453 181.05 139.779 184.018 139.899Z","fill","#ADE0EC"],["id","g352"],["id","path354","d","M478.281 145.979L473.499 145.088L471.809 150.637L476.591 151.528L478.281 145.979ZM483.567 146.965L481.877 152.514L486.737 153.418L485.812 158.499L480.333 157.478L478.528 163.209L473.241 162.224L475.046 156.492L470.263 155.601L468.46 161.331L463.174 160.347L464.977 154.616L460.079 153.702L461.001 148.622L466.522 149.65L468.214 144.102L463.314 143.19L464.237 138.109L469.759 139.138L471.562 133.407L476.848 134.393L475.043 140.124L479.826 141.015L481.629 135.284L486.917 136.269L485.112 142.001L490.01 142.913L489.088 147.994L483.567 146.965Z","fill","#ADE0EC"],["id","g356"],["id","path358","d","M230.094 489.727H164.645C144.782 489.727 128.679 473.412 128.679 453.286C128.679 433.159 144.782 416.844 164.645 416.844H194.128C213.99 416.844 230.094 433.159 230.094 453.286V489.727Z","fill","#FFBF69"],["id","g360"],["id","path362","d","M190.288 474.567C192.225 471.057 193.491 467.457 194.24 463.884C197.265 463.216 199.718 462.418 201.535 461.712C199.468 467.269 195.439 471.849 190.288 474.567ZM173.549 476.516C170.414 472.301 168.399 468.049 167.204 463.913C172.228 464.889 176.849 465.295 180.987 465.295C184.501 465.295 187.666 465.013 190.478 464.585C189.44 468.665 187.643 472.75 184.795 476.628C183.054 477.042 181.249 477.283 179.386 477.283C177.368 477.283 175.42 476.999 173.549 476.516ZM157.077 461.27C159.25 461.983 161.355 462.573 163.406 463.075C164.255 466.725 165.672 470.467 167.822 474.207C162.852 471.377 159.006 466.783 157.077 461.27ZM166.919 432.92C165.905 435.193 164.777 438.165 163.89 441.631C161.455 442.199 159.416 442.847 157.807 443.446C159.751 439.087 162.942 435.428 166.919 432.92ZM185.694 430.179C186.289 431.348 188.269 435.45 189.79 441.13C180.926 439.619 173.434 439.938 167.6 440.897C169.168 435.61 171.267 431.824 172.077 430.47C174.382 429.71 176.835 429.288 179.386 429.288C181.572 429.288 183.682 429.614 185.694 430.179ZM201.203 443.946C198.569 443.098 196.02 442.407 193.568 441.864C192.612 437.856 191.394 434.47 190.4 432.058C195.218 434.635 199.063 438.835 201.203 443.946ZM194.354 445.71C196.968 446.339 199.688 447.138 202.507 448.133C202.868 449.796 203.071 451.515 203.071 453.285C203.071 454.669 202.929 456.014 202.707 457.334C201.441 457.942 198.765 459.081 194.862 460.045C195.44 454.989 195.108 450.091 194.354 445.71ZM166.64 444.734C172.634 443.581 180.793 443.047 190.668 444.909C191.612 449.668 192.068 455.159 191.237 460.804C184.963 461.903 176.497 462.275 166.311 460.097C165.321 454.509 165.701 449.252 166.64 444.734ZM155.701 453.285C155.701 451.44 155.927 449.649 156.319 447.921C157.561 447.343 159.839 446.402 163.05 445.549C162.325 449.694 162.056 454.341 162.712 459.24C160.557 458.67 158.328 457.976 156.039 457.161C155.835 455.896 155.701 454.608 155.701 453.285ZM179.386 425.733C164.391 425.733 152.192 438.093 152.192 453.285C152.192 468.479 164.391 480.838 179.386 480.838C194.381 480.838 206.58 468.479 206.58 453.285C206.58 438.093 194.381 425.733 179.386 425.733Z","fill","#FAFAFA"],["id","g364"],["id","path366","d","M487.575 534.716H553.024C572.888 534.716 588.99 518.4 588.99 498.275C588.99 478.149 572.888 461.834 553.024 461.834H523.541C503.679 461.834 487.575 478.149 487.575 498.275V534.716Z","fill","#FFBF69"],["id","g368"],["id","path370","d","M565.214 487.805C565.214 477.497 549.034 468.633 538.283 477.531C527.532 468.633 511.352 477.497 511.352 487.805C511.352 487.805 507.872 508.014 538.283 522.676C568.694 508.014 565.214 487.805 565.214 487.805Z","stroke","#FAFAFA","stroke-width","3.811","stroke-miterlimit","10","stroke-linejoin","round"],["id","g372"],["id","path374","d","M466.093 53.4869C465.677 53.3258 465.259 53.1899 464.843 53.074C464.729 52.6558 464.594 52.2389 464.437 51.8207C463.767 50.1411 462.888 48.4615 461.12 46.7819C459.352 48.4615 458.474 50.1411 457.804 51.8207C457.645 52.2415 457.51 52.6638 457.395 53.0847C456.978 53.2019 456.563 53.3391 456.147 53.4989C454.489 54.1782 452.832 55.0679 451.174 56.8594C452.832 58.6509 454.489 59.5406 456.147 60.2199C456.56 60.3797 456.973 60.5156 457.384 60.6315C457.499 61.0537 457.633 61.4759 457.792 61.8982C458.46 63.5777 459.342 65.2573 461.12 66.9369C462.899 65.2573 463.781 63.5777 464.449 61.8982C464.605 61.4799 464.741 61.0617 464.855 60.6421C465.267 60.5276 465.681 60.3917 466.093 60.2319C467.751 59.5553 469.409 58.6615 471.067 56.8594C469.409 55.0573 467.751 54.1635 466.093 53.4869Z","fill","#ADE0EC"],["id","star1"],["id","path378","d","M18.666 335.315C18.2493 335.154 17.8325 335.016 17.4145 334.901C17.3001 334.484 17.166 334.067 17.0096 333.649C16.3392 331.968 15.461 330.289 13.6929 328.61C11.9247 330.289 11.0466 331.968 10.3761 333.649C10.2171 334.069 10.0816 334.492 9.96728 334.913C9.55186 335.028 9.13514 335.167 8.71972 335.327C7.06201 336.006 5.4043 336.896 3.74658 338.687C5.4043 340.479 7.06201 341.369 8.71972 342.048C9.13251 342.206 9.54398 342.342 9.95676 342.458C10.0698 342.882 10.2052 343.304 10.3643 343.725C11.0321 345.406 11.9142 347.085 13.6929 348.765C15.4715 347.085 16.3536 345.406 17.0214 343.725C17.1779 343.308 17.3133 342.89 17.4263 342.47C17.8391 342.354 18.2532 342.22 18.666 342.058C20.3237 341.383 21.9814 340.489 23.6391 338.687C21.9814 336.885 20.3237 335.991 18.666 335.315Z","fill","#ADE0EC"],["id","g380"],["id","path382","d","M500.378 253.717C499.962 253.558 499.545 253.42 499.128 253.305C499.014 252.886 498.878 252.469 498.722 252.052C498.052 250.372 497.173 248.692 495.405 247.012C493.637 248.692 492.759 250.372 492.089 252.052C491.931 252.472 491.795 252.894 491.681 253.317C491.264 253.432 490.849 253.57 490.433 253.729C488.774 254.409 487.117 255.298 485.459 257.09C487.117 258.881 488.774 259.772 490.433 260.45C490.845 260.61 491.258 260.746 491.669 260.862C491.784 261.284 491.918 261.706 492.078 262.129C492.745 263.808 493.627 265.488 495.405 267.167C497.184 265.488 498.066 263.808 498.734 262.129C498.892 261.71 499.026 261.292 499.14 260.874C499.553 260.758 499.966 260.622 500.378 260.462C502.037 259.786 503.694 258.892 505.352 257.09C503.694 255.289 502.037 254.395 500.378 253.717Z","fill","#ADE0EC"],["id","g384"],["id","path386","d","M673.413 79.5778C673.204 79.4978 672.995 79.4286 672.785 79.3713C672.729 79.1622 672.662 78.9517 672.583 78.7426C672.246 77.9008 671.806 77.059 670.921 76.2172C670.035 77.059 669.595 77.9008 669.258 78.7426C669.178 78.9544 669.112 79.1648 669.054 79.3766C668.844 79.4352 668.636 79.5032 668.429 79.5844C667.596 79.9241 666.766 80.3703 665.936 81.2693C666.766 82.1657 667.596 82.6119 668.429 82.9529C668.635 83.0328 668.84 83.1008 669.048 83.158C669.106 83.3698 669.173 83.5816 669.253 83.7947C669.587 84.6352 670.03 85.4769 670.921 86.3201C671.811 85.4769 672.254 84.6352 672.589 83.7947C672.668 83.5842 672.734 83.3738 672.792 83.1647C672.999 83.1061 673.206 83.0382 673.413 82.9596C674.244 82.6199 675.075 82.1711 675.906 81.2693C675.075 80.3649 674.244 79.9174 673.413 79.5778Z","fill","#D0F6FF"],["id","g388"],["id","path390","d","M724.621 229.528C724.413 229.448 724.204 229.379 723.994 229.321C723.936 229.112 723.87 228.902 723.791 228.694C723.455 227.851 723.014 227.009 722.128 226.167C721.244 227.009 720.803 227.851 720.467 228.694C720.387 228.904 720.32 229.115 720.262 229.327C720.053 229.385 719.845 229.453 719.636 229.534C718.805 229.874 717.974 230.32 717.145 231.219C717.974 232.116 718.805 232.562 719.636 232.903C719.842 232.983 720.049 233.051 720.256 233.108C720.314 233.32 720.38 233.532 720.46 233.745C720.795 234.585 721.238 235.427 722.128 236.27C723.02 235.427 723.461 234.585 723.797 233.745C723.877 233.534 723.943 233.324 723.999 233.115C724.208 233.056 724.415 232.988 724.621 232.91C725.453 232.57 726.284 232.121 727.113 231.219C726.284 230.315 725.453 229.867 724.621 229.528Z","fill","#D0F6FF"],["id","g392"],["id","path394","d","M722.669 226.015C722.46 225.935 722.251 225.866 722.042 225.809C721.984 225.6 721.918 225.389 721.838 225.18C721.503 224.338 721.063 223.497 720.177 222.655C719.291 223.497 718.85 224.338 718.515 225.18C718.435 225.392 718.368 225.602 718.31 225.814C718.101 225.873 717.892 225.941 717.684 226.022C716.853 226.362 716.022 226.808 715.192 227.707C716.022 228.603 716.853 229.049 717.684 229.39C717.891 229.47 718.097 229.538 718.305 229.595C718.361 229.807 718.428 230.019 718.508 230.232C718.844 231.073 719.285 231.914 720.177 232.758C721.068 231.914 721.51 231.073 721.845 230.232C721.924 230.022 721.991 229.811 722.047 229.602C722.255 229.544 722.463 229.476 722.669 229.397C723.5 229.057 724.331 228.609 725.162 227.707C724.331 226.802 723.5 226.355 722.669 226.015Z","fill","#D0F6FF"],["id","g396"],["id","path398","d","M122.37 271.837C122.161 271.756 121.952 271.688 121.742 271.63C121.686 271.421 121.619 271.211 121.54 271.002C121.203 270.16 120.763 269.318 119.877 268.476C118.991 269.318 118.551 270.16 118.215 271.002C118.135 271.213 118.068 271.424 118.01 271.636C117.801 271.694 117.594 271.762 117.385 271.842C116.554 272.183 115.723 272.629 114.892 273.527C115.723 274.425 116.554 274.871 117.385 275.212C117.591 275.291 117.797 275.36 118.005 275.417C118.062 275.629 118.129 275.841 118.209 276.052C118.544 276.894 118.986 277.736 119.877 278.578C120.768 277.736 121.211 276.894 121.545 276.052C121.624 275.843 121.691 275.633 121.748 275.422C121.955 275.365 122.163 275.297 122.37 275.217C123.2 274.878 124.031 274.43 124.862 273.527C124.031 272.624 123.2 272.176 122.37 271.837Z","fill","#ADE0EC"],["id","g400"],["id","path402","d","M30.9696 538.087C30.7606 538.007 30.5516 537.939 30.3426 537.881C30.2847 537.671 30.219 537.461 30.1401 537.252C29.8036 536.41 29.3632 535.568 28.4772 534.728C27.5911 535.568 27.1507 536.41 26.8155 537.252C26.7353 537.464 26.6683 537.674 26.6104 537.887C26.4014 537.945 26.1937 538.012 25.9847 538.094C25.1538 538.435 24.323 538.881 23.4922 539.779C24.323 540.675 25.1538 541.121 25.9847 541.462C26.1911 541.542 26.3975 541.611 26.6052 541.667C26.6617 541.88 26.7301 542.092 26.8089 542.303C27.1442 543.146 27.5859 543.988 28.4772 544.829C29.3685 543.988 29.8115 543.146 30.1454 542.303C30.2243 542.094 30.2913 541.884 30.3478 541.674C30.5555 541.615 30.7633 541.549 30.9696 541.468C31.8005 541.128 32.6313 540.68 33.4621 539.779C32.6313 538.876 31.8005 538.427 30.9696 538.087Z","fill","#ADE0EC"],["id","g404"],["id","path406","d","M384.68 138.195C384.471 138.114 384.262 138.046 384.053 137.989C383.995 137.78 383.928 137.569 383.849 137.36C383.514 136.518 383.073 135.676 382.187 134.835C381.301 135.676 380.861 136.518 380.524 137.36C380.445 137.572 380.377 137.782 380.32 137.994C380.111 138.053 379.904 138.121 379.695 138.202C378.864 138.541 378.033 138.988 377.202 139.885C378.033 140.783 378.864 141.229 379.695 141.57C379.901 141.65 380.107 141.718 380.314 141.775C380.372 141.987 380.439 142.199 380.519 142.411C380.854 143.253 381.296 144.094 382.187 144.936C383.078 144.094 383.52 143.253 383.855 142.411C383.934 142.202 384.001 141.991 384.058 141.781C384.266 141.723 384.472 141.656 384.68 141.576C385.51 141.236 386.341 140.788 387.172 139.885C386.341 138.982 385.51 138.535 384.68 138.195Z","fill","#ADE0EC"],["id","g408"],["id","path410","d","M143.253 52.4684C143.044 52.3885 142.835 52.3192 142.626 52.262C142.568 52.0528 142.501 51.8424 142.423 51.6333C142.087 50.7915 141.646 49.9497 140.76 49.1079C139.874 49.9497 139.434 50.7915 139.097 51.6333C139.019 51.8451 138.951 52.0555 138.894 52.2673C138.685 52.3259 138.477 52.3938 138.268 52.4751C137.437 52.8147 136.606 53.2609 135.775 54.1586C136.606 55.0564 137.437 55.5026 138.268 55.8436C138.474 55.9235 138.681 55.9914 138.888 56.0487C138.945 56.2605 139.012 56.4722 139.092 56.6854C139.427 57.5258 139.869 58.3676 140.76 59.2107C141.652 58.3676 142.093 57.5258 142.429 56.6854C142.507 56.4749 142.575 56.2645 142.631 56.0553C142.839 55.9967 143.045 55.9288 143.253 55.8502C144.084 55.5106 144.915 55.0617 145.745 54.1586C144.915 53.2556 144.084 52.8081 143.253 52.4684Z","fill","#ADE0EC"],["id","star4"],["id","path414","d","M659.175 279.551C658.966 279.47 658.757 279.402 658.546 279.344C658.49 279.135 658.423 278.925 658.344 278.716C658.009 277.874 657.567 277.032 656.682 276.19C655.796 277.032 655.356 277.874 655.019 278.716C654.939 278.926 654.873 279.138 654.816 279.35C654.605 279.408 654.397 279.476 654.19 279.556C653.359 279.897 652.527 280.343 651.697 281.241C652.527 282.139 653.359 282.585 654.19 282.926C654.396 283.005 654.603 283.074 654.81 283.131C654.867 283.343 654.934 283.555 655.014 283.766C655.349 284.608 655.791 285.45 656.682 286.292C657.574 285.45 658.015 284.608 658.35 283.766C658.429 283.557 658.495 283.347 658.553 283.136C658.761 283.079 658.968 283.011 659.175 282.931C660.006 282.592 660.836 282.144 661.667 281.241C660.836 280.338 660.006 279.89 659.175 279.551Z","fill","#ADE0EC"],["id","star5"],["id","path418","d","M412.477 191.341C412.268 191.26 412.059 191.192 411.85 191.133C411.793 190.924 411.727 190.715 411.647 190.506C411.311 189.664 410.871 188.822 409.985 187.98C409.099 188.822 408.659 189.664 408.323 190.506C408.243 190.718 408.176 190.928 408.118 191.14C407.909 191.197 407.7 191.266 407.492 191.346C406.662 191.687 405.831 192.133 405 193.031C405.831 193.929 406.662 194.375 407.492 194.715C407.699 194.795 407.905 194.864 408.113 194.921C408.17 195.133 408.237 195.345 408.317 195.556C408.652 196.398 409.094 197.24 409.985 198.082C410.876 197.24 411.318 196.398 411.653 195.556C411.732 195.346 411.799 195.137 411.856 194.926C412.063 194.869 412.271 194.801 412.477 194.721C413.308 194.382 414.139 193.934 414.97 193.031C414.139 192.128 413.308 191.681 412.477 191.341Z","fill","#D0F6FF"],["id","star2"],["id","path422","d","M318.495 91.4014C318.129 91.2602 317.762 91.1403 317.396 91.0391C317.295 90.6715 317.178 90.3039 317.04 89.9363C316.45 88.4605 315.678 86.9847 314.124 85.5075C312.57 86.9847 311.797 88.4605 311.208 89.9363C311.069 90.3079 310.95 90.6782 310.85 91.0484C310.483 91.151 310.117 91.2709 309.752 91.4121C308.295 92.0088 306.837 92.792 305.381 94.3663C306.837 95.9407 308.295 96.7239 309.752 97.3206C310.115 97.4604 310.476 97.5803 310.839 97.6815C310.939 98.0531 311.059 98.4234 311.198 98.795C311.786 100.272 312.56 101.749 314.124 103.225C315.687 101.749 316.463 100.272 317.049 98.795C317.187 98.4274 317.306 98.0598 317.406 97.6922C317.77 97.5896 318.132 97.4711 318.495 97.3312C319.953 96.7358 321.41 95.95 322.868 94.3663C321.41 92.7826 319.953 91.9968 318.495 91.4014Z","fill","#ADE0EC"],["id","g424"],["id","path426","d","M95.3161 198.94C94.9494 198.801 94.5826 198.679 94.2171 198.578C94.1159 198.21 93.9989 197.843 93.8609 197.475C93.2706 195.999 92.4989 194.524 90.9451 193.047C89.3912 194.524 88.6182 195.999 88.0293 197.475C87.8899 197.847 87.7703 198.217 87.6704 198.587C87.3036 198.69 86.9382 198.81 86.5727 198.951C85.1161 199.548 83.6582 200.331 82.2017 201.905C83.6582 203.48 85.1161 204.263 86.5727 204.86C86.9355 204.999 87.2971 205.119 87.6599 205.221C87.7598 205.592 87.8794 205.962 88.0188 206.334C88.6064 207.811 89.3807 209.288 90.9451 210.764C92.5081 209.288 93.2838 207.811 93.8701 206.334C94.0081 205.966 94.1264 205.599 94.2263 205.231C94.5892 205.129 94.9533 205.01 95.3161 204.87C96.774 204.275 98.2306 203.49 99.6885 201.905C98.2306 200.322 96.774 199.536 95.3161 198.94Z","fill","#ADE0EC"],["id","star3"],["id","path430","d","M567.016 163.164C566.649 163.023 566.282 162.903 565.915 162.8C565.815 162.434 565.697 162.066 565.559 161.699C564.97 160.223 564.197 158.746 562.643 157.27C561.089 158.746 560.316 160.223 559.728 161.699C559.59 162.069 559.47 162.441 559.369 162.81C559.003 162.912 558.638 163.033 558.272 163.175C556.814 163.771 555.358 164.553 553.9 166.129C555.358 167.703 556.814 168.486 558.272 169.082C558.634 169.222 558.997 169.343 559.359 169.444C559.459 169.816 559.579 170.186 559.717 170.558C560.306 172.035 561.08 173.51 562.643 174.986C564.206 173.51 564.982 172.035 565.57 170.558C565.708 170.19 565.826 169.822 565.926 169.453C566.289 169.352 566.653 169.234 567.016 169.094C568.472 168.498 569.93 167.713 571.387 166.129C569.93 164.545 568.472 163.759 567.016 163.164Z","fill","#D0F6FF"],["id","star6"],["id","path434","d","M785.486 113.408C785.119 113.267 784.752 113.147 784.385 113.045C784.285 112.678 784.167 112.31 784.03 111.943C783.44 110.467 782.667 108.99 781.113 107.514C779.559 108.99 778.786 110.467 778.198 111.943C778.059 112.314 777.94 112.685 777.839 113.055C777.473 113.157 777.108 113.277 776.742 113.418C775.284 114.015 773.828 114.798 772.37 116.373C773.828 117.947 775.284 118.73 776.742 119.327C777.104 119.467 777.467 119.587 777.829 119.688C777.929 120.06 778.049 120.43 778.187 120.801C778.776 122.279 779.55 123.756 781.113 125.231C782.676 123.756 783.452 122.279 784.04 120.801C784.178 120.434 784.296 120.066 784.396 119.697C784.759 119.596 785.123 119.477 785.486 119.338C786.942 118.742 788.4 117.956 789.857 116.373C788.4 114.789 786.942 114.003 785.486 113.408Z","fill","#D0F6FF"],["id","g436"],["id","path438","d","M556.27 45.0362C555.903 44.895 555.536 44.7752 555.169 44.6739C555.069 44.3063 554.951 43.9387 554.813 43.5711C554.224 42.0953 553.451 40.6182 551.897 39.1424C550.343 40.6182 549.57 42.0953 548.983 43.5711C548.843 43.9427 548.724 44.313 548.624 44.6833C548.257 44.7858 547.892 44.9057 547.526 45.0469C546.068 45.6436 544.612 46.4268 543.154 48.0011C544.612 49.5755 546.068 50.3587 547.526 50.9554C547.888 51.0953 548.251 51.2151 548.613 51.3164C548.713 51.688 548.833 52.0583 548.971 52.4299C549.56 53.907 550.334 55.3841 551.897 56.8599C553.46 55.3841 554.236 53.907 554.824 52.4299C554.962 52.0622 555.08 51.6946 555.18 51.327C555.543 51.2245 555.907 51.1059 556.27 50.9661C557.726 50.3707 559.184 49.5848 560.641 48.0011C559.184 46.4175 557.726 45.6316 556.27 45.0362Z","fill","#D0F6FF"],["id","contact-form","action","https://formspree.io/f/mldrlygg","method","POST",3,"submit"],[1,"title","text-center","mb-4"],[1,"form-group","position-relative"],["for","name",1,"d-block"],["data-feather","user",1,"icon"],["type","text","id","name","name","name","placeholder","Name","required","",1,"form-control","form-control-lg","thick"],["for","email",1,"d-block"],["data-feather","mail",1,"icon"],["type","email","id","email","name","email","placeholder","E-mail","required","",1,"form-control","form-control-lg","thick"],[1,"form-group","message"],["id","message","name","message","rows","7","placeholder","Message","required","",1,"form-control","form-control-lg"],[1,"text-center"],["type","submit",1,"btn","btn-primary"],[3,"ngClass",4,"ngIf"],[3,"ngClass"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"h2",1),N(2,"Contact me "),p()(),h(3,"div",2),Lr(),h(4,"svg",3)(5,"g",4)(6,"g",5)(7,"g",6)(8,"g",7),v(9,"path",8),p(),h(10,"g",9),v(11,"path",10),p(),h(12,"g",11),v(13,"path",12),p(),h(14,"g",13),v(15,"path",14),p(),h(16,"g",15),v(17,"path",16),p(),h(18,"g",17),v(19,"path",18),p(),h(20,"g",19),v(21,"path",20),p(),h(22,"g",21),v(23,"path",22),p(),h(24,"g",23),v(25,"path",24),p(),h(26,"g",25),v(27,"path",26),p(),h(28,"g",27),v(29,"path",28),p(),h(30,"g",29),v(31,"path",30),p(),h(32,"g",31),v(33,"path",32),p(),h(34,"g",33),v(35,"path",34),p(),h(36,"g",35),v(37,"path",36),p(),h(38,"g",37),v(39,"path",38),p(),h(40,"g",39),v(41,"path",40),p(),h(42,"g",41),v(43,"path",42),p(),h(44,"g",43),v(45,"path",44),p(),h(46,"g",45),v(47,"path",46),p(),h(48,"g",47),v(49,"path",48),p(),h(50,"g",49),v(51,"path",50),p(),h(52,"g",51),v(53,"path",52),p(),h(54,"g",53),v(55,"path",54),p(),h(56,"g",55),v(57,"path",56),p(),h(58,"g",57),v(59,"path",58),p(),h(60,"g",59),v(61,"path",60),p(),h(62,"g",61),v(63,"path",62),p(),h(64,"g",63),v(65,"path",64),p(),h(66,"g",65),v(67,"path",66),p(),h(68,"g",67),v(69,"path",68),p(),h(70,"g",69),v(71,"path",70),p(),h(72,"g",71),v(73,"path",72),p(),h(74,"g",73),v(75,"path",74),p(),h(76,"g",75),v(77,"path",76),p(),h(78,"g",77),v(79,"path",78),p(),h(80,"g",79),v(81,"path",80),p(),h(82,"g",81),v(83,"path",82),p(),h(84,"g",83),v(85,"path",84),p(),h(86,"g",85),v(87,"path",86),p(),v(88,"path",87)(89,"path",88)(90,"path",89)(91,"path",90)(92,"path",91),h(93,"g",92),v(94,"path",93),p(),h(95,"g",94),v(96,"path",95),p(),h(97,"g",96),v(98,"path",97),p(),h(99,"g",98),v(100,"path",99),p(),h(101,"g",100),v(102,"path",101),p(),v(103,"path",102)(104,"path",103)(105,"path",104)(106,"path",105)(107,"path",106)(108,"path",107)(109,"path",108)(110,"path",109)(111,"path",110)(112,"path",111)(113,"path",112)(114,"path",113),h(115,"g",114),v(116,"path",115),p(),h(117,"g",116),v(118,"path",117),p(),v(119,"path",118),h(120,"g",119),v(121,"path",120),p(),h(122,"g",121),v(123,"path",122),p(),v(124,"path",123),h(125,"g",124),v(126,"path",125),p(),h(127,"g",126),v(128,"path",127),p(),h(129,"g",128)(130,"g",129)(131,"g",130)(132,"g",131),v(133,"path",132),p(),h(134,"g",133),v(135,"path",134),p()()()(),h(136,"g",135)(137,"g",136)(138,"g",137)(139,"g",138),v(140,"path",139),p(),h(141,"g",140),v(142,"path",141),p()()()(),h(143,"g",142)(144,"g",143),v(145,"path",144),p(),v(146,"path",145)(147,"path",146)(148,"path",147)(149,"path",148)(150,"path",149)(151,"path",150)(152,"path",151)(153,"path",152),h(154,"g",153),v(155,"path",154),p(),h(156,"g",155),v(157,"path",156),p(),h(158,"g",157),v(159,"path",158),p()(),h(160,"g",159),v(161,"path",160),p(),h(162,"g",161),v(163,"path",162),p(),h(164,"g",163),v(165,"path",164),p(),h(166,"g",165),v(167,"path",166),p(),h(168,"g",167),v(169,"path",168),p(),h(170,"g",169),v(171,"path",170),p(),h(172,"g",171),v(173,"path",172),p(),h(174,"g",173),v(175,"path",174),p(),h(176,"g",175),v(177,"path",176),p(),h(178,"g",177),v(179,"path",178),p(),h(180,"g",179),v(181,"path",180),p(),h(182,"g",181),v(183,"path",182),p(),h(184,"g",183),v(185,"path",184),p(),h(186,"g",185),v(187,"path",186),p(),h(188,"g",187),v(189,"path",188),p(),h(190,"g",189),v(191,"path",190),p(),h(192,"g",191),v(193,"path",192),p(),h(194,"g",193),v(195,"path",194),p(),h(196,"g",195),v(197,"path",196),p(),h(198,"g",197),v(199,"path",198),p(),h(200,"g",199),v(201,"path",200),p(),h(202,"g",201),v(203,"path",202),p(),h(204,"g",203),v(205,"path",204),p()()()()(),Vr(),h(206,"form",205),Wt("submit",function(s){return i.onSubmit(s)}),h(207,"h1",206),N(208,"Talk to me"),p(),h(209,"div",207)(210,"label",208),v(211,"i",209),p(),v(212,"input",210),p(),h(213,"div",207)(214,"label",211),v(215,"i",212),p(),v(216,"input",213),p(),h(217,"div",214),v(218,"textarea",215),p(),h(219,"div",216)(220,"button",217),N(221,"Send message"),p()()(),ot(222,rI,2,5,"div",218),p()),r&2&&(J(222),me("ngIf",i.submissionStatus))},dependencies:[e0,_a,v1,g1,Kd],styles:['*[_ngcontent-%COMP%]{margin-bottom:1rem}.contact-header[_ngcontent-%COMP%]{text-align:center;color:var(--text-primary);padding:1rem;position:relative;font-weight:600;font-size:2rem}.contact-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:150px;height:4px;background-color:var(--accent-pink);border-radius:2px}.container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(2rem,8vw,9rem);max-width:100vw;padding:0 1rem}svg[_ngcontent-%COMP%]{width:30%;height:auto;animation:_ngcontent-%COMP%_float 2s ease-in-out infinite;margin-left:10%}form[_ngcontent-%COMP%]{width:35%;background-color:#fff;padding:2rem;border-radius:3rem;box-shadow:0 10px 20px #0000001a;margin-right:10%}.title[_ngcontent-%COMP%]{font-weight:600;color:var(--text-primary);font-size:2.5rem;text-align:center;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;font-size:1.1rem;color:var(--text-secondary);background-color:var(--primary-pink-lightest);border:none;border-radius:2rem;box-shadow:0 7px 5px var(--shadow-pink)}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:7rem}.form-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:var(--primary-pink)}[_ngcontent-%COMP%]::placeholder{color:var(--primary-pink-light);font-weight:600}.btn.btn-primary[_ngcontent-%COMP%]{display:inline-block;width:100%;padding:.8rem;font-size:1.1rem;font-weight:700;border:none;border-radius:3rem;background:linear-gradient(131deg,var(--primary-pink),var(--accent-pink),var(--primary-pink-light),var(--primary-pink-dark));background-size:300% 100%;transition:all .3s ease-in-out;color:#fff;cursor:pointer}.btn.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 7px 5px var(--shadow-pink);background-size:100% 100%;transform:translateY(-.15em)}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}@keyframes _ngcontent-%COMP%_blink{0%{opacity:0}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.container[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:2rem}svg[_ngcontent-%COMP%]{width:60%;margin-left:0}form[_ngcontent-%COMP%]{width:90%;margin-right:0}.title[_ngcontent-%COMP%]{font-size:2rem}.btn.btn-primary[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.title[_ngcontent-%COMP%]{font-size:1.8rem}.contact-header[_ngcontent-%COMP%]{font-size:1.5rem}form[_ngcontent-%COMP%]{padding:1.5rem}.btn.btn-primary[_ngcontent-%COMP%]{padding:.6rem}}']})}return e})();var wy=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/skills";constructor(n){this.http=n}getSkills(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(_(en))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function sI(e,t){if(e&1&&(h(0,"div",6)(1,"div",7),v(2,"img",8),p(),h(3,"h3",9),N(4),p()()),e&2){let n=t.$implicit;J(2),me("src",n.icon,Zn)("alt",n.name),J(2),at(n.name)}}var Dy=(()=>{class e{skillsService;skills=[];constructor(n){this.skillsService=n}ngOnInit(){this.skillsService.getSkills().subscribe(n=>{this.skills=n.map(r=>({icon:r.photo,name:r.name}))},n=>{console.error("Error fetching skills:",n)})}static \u0275fac=function(r){return new(r||e)($(wy))};static \u0275cmp=ge({type:e,selectors:[["app-skills"]],decls:7,vars:1,consts:[["id","skills",1,"pt-16","pb-12","bg-gradient-to-br","from-pink-50","via-white","to-rose-100"],[1,"text-center","pb-10"],[1,"text-4xl","font-extrabold","uppercase","text-pink-600","tracking-wider","relative","inline-block"],[1,"block","w-16","h-1","bg-pink-400","mx-auto","mt-2","rounded-full"],[1,"grid","grid-cols-2","sm:grid-cols-3","md:grid-cols-4","lg:grid-cols-5","gap-6","px-6","sm:px-12"],["class","bg-white border border-pink-200 rounded-3xl p-6 shadow-md hover:shadow-pink-200 transition-all duration-300 hover:scale-105 flex flex-col items-center text-center",4,"ngFor","ngForOf"],[1,"bg-white","border","border-pink-200","rounded-3xl","p-6","shadow-md","hover:shadow-pink-200","transition-all","duration-300","hover:scale-105","flex","flex-col","items-center","text-center"],[1,"w-20","h-20","flex","items-center","justify-center","rounded-full","bg-pink-100","border","border-pink-300","shadow-inner","mb-3"],[1,"w-10","h-10","object-contain",3,"src","alt"],[1,"text-pink-700","font-semibold","text-sm","tracking-wide"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"div",1)(2,"h2",2),N(3," Skills "),v(4,"span",3),p()(),h(5,"div",4),ot(6,sI,5,3,"div",5),p()()),r&2&&(J(6),me("ngForOf",i.skills))},dependencies:[Wr]})}return e})();var Ey=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/experiences";constructor(n){this.http=n}getExperiences(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(_(en))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function cI(e,t){if(e&1&&(h(0,"div",12)(1,"h3",13),N(2),p(),h(3,"p",14),N(4),p(),h(5,"p",15),N(6),xi(7,"date"),xi(8,"date"),p()()),e&2){let n=qt().$implicit;J(2),at(n.title),J(2),at(n.company),J(2),ia("",Fi(7,4,n.from,"MMM yyyy")," - ",n.to?Fi(8,7,n.to,"MMM yyyy"):"Present","")}}function uI(e,t){if(e&1&&(h(0,"div",16)(1,"h3",13),N(2),p(),h(3,"p",14),N(4),p(),h(5,"p",15),N(6),xi(7,"date"),xi(8,"date"),p()()),e&2){let n=qt().$implicit;J(2),at(n.title),J(2),at(n.company),J(2),ia("",Fi(7,4,n.from,"MMM yyyy")," - ",n.to?Fi(8,7,n.to,"MMM yyyy"):"Present","")}}function dI(e,t){if(e&1&&(h(0,"div",6)(1,"div",7),ot(2,cI,9,10,"div",8),p(),v(3,"div",9),h(4,"div",10),ot(5,uI,9,10,"div",11),p()()),e&2){let n=t.index;J(2),me("ngIf",n%2===0),J(3),me("ngIf",n%2!==0)}}var by=(()=>{class e{experiencesService;experiences=[];skills;constructor(n){this.experiencesService=n}ngOnInit(){this.experiencesService.getExperiences().subscribe(n=>{this.experiences=n.map(r=>({title:r.title,company:r.company,from:r.from,to:r.to}))},n=>{console.error("Error fetching experiences:",n)})}static \u0275fac=function(r){return new(r||e)($(Ey))};static \u0275cmp=ge({type:e,selectors:[["app-experiences"]],decls:7,vars:1,consts:[["id","experience",1,"pt-10"],[1,"text-center","pb-8"],[1,"text-4xl","font-righteous","uppercase","theme-text-primary","mb-2","relative","inline-block","after:block","after:w-24","after:h-1","after:bg-pink-400","after:rounded","after:mx-auto","after:mt-2"],[1,"relative","max-w-5xl","mx-auto","px-4"],[1,"absolute","left-1/2","transform","-translate-x-1/2","h-full","border-l-2","border-pink-400"],["class","mb-16 flex flex-col sm:flex-row items-center w-full relative",4,"ngFor","ngForOf"],[1,"mb-16","flex","flex-col","sm:flex-row","items-center","w-full","relative"],[1,"w-full","sm:w-1/2","pr-4","sm:pr-8","flex","justify-end","z-10"],["class","bg-white border border-pink-200 p-6 rounded-lg shadow w-full max-w-md text-right",4,"ngIf"],[1,"absolute","left-1/2","transform","-translate-x-1/2","w-5","h-5","bg-pink-400","rounded-full","border-4","border-white","shadow","z-20"],[1,"w-full","sm:w-1/2","pl-4","sm:pl-8","flex","justify-start","z-10"],["class","bg-white border border-pink-200 p-6 rounded-lg shadow w-full max-w-md text-left",4,"ngIf"],[1,"bg-white","border","border-pink-200","p-6","rounded-lg","shadow","w-full","max-w-md","text-right"],[1,"text-xl","font-semibold","theme-text-secondary"],[1,"text-pink-600"],[1,"text-pink-500","text-sm"],[1,"bg-white","border","border-pink-200","p-6","rounded-lg","shadow","w-full","max-w-md","text-left"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"div",1)(2,"h2",2),N(3," Experience "),p()(),h(4,"div",3),v(5,"div",4),ot(6,dI,6,2,"div",5),p()()),r&2&&(J(6),me("ngForOf",i.experiences))},dependencies:[Wr,_a,t0]})}return e})();var _y=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=ge({type:e,selectors:[["app-footer"]],decls:17,vars:0,consts:[[1,"theme-bg-gradient","text-white","py-1"],[1,"flex","justify-center","gap-4"],[1,"text-sm","hover:text-pink-200","transition-colors"],["href","mailto:<EMAIL>",1,"theme-text-secondary"],["href","https://www.instagram.com/roaaayman_10/",1,"theme-text-secondary"],["href","https://github.com/roaaayman21",1,"theme-text-secondary"],["href","https://wa.me/+2001151310078",1,"theme-text-secondary"],[1,"text-xl"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"ul",1)(2,"li",2)(3,"a",3),N(4,"Email"),p()(),h(5,"li",2)(6,"a",4),N(7,"Instagram"),p()(),h(8,"li",2)(9,"a",5),N(10,"Github"),p()(),h(11,"li",2)(12,"a",6),N(13,"WhatsApp"),p()(),h(14,"li",7)(15,"p"),N(16,"\u{1F44B}"),p()()()())},styles:["div[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;width:100%;background:linear-gradient(to right,var(--primary-pink-lightest),var(--bg-gradient-middle),var(--rose-lighter));background-size:cover;flex-direction:column}ul[_ngcontent-%COMP%]{display:inline-grid;grid-auto-flow:row;grid-gap:12px;justify-items:center;margin:auto;padding:10px;list-style:none;text-align:center}@media (min-width: 500px){ul[_ngcontent-%COMP%]{grid-auto-flow:column}}li[_ngcontent-%COMP%]{padding:5px}a[_ngcontent-%COMP%]{color:var(--text-secondary);text-decoration:none;font-size:1.2rem;box-shadow:inset 0 -1px 0 var(--border-pink)}a[_ngcontent-%COMP%]:hover{box-shadow:inset 0 -1.2em 0 var(--primary-pink-lighter)}li[_ngcontent-%COMP%]:last-child{grid-column:1 / 2;grid-row:1 / 2}li[_ngcontent-%COMP%]:hover ~ li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_wave-animation .3s infinite}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-15deg)}}@media (max-width: 500px){div[_ngcontent-%COMP%]{background-size:200% 100%;background-position:center}}"]})}return e})();var Iy=(()=>{class e{title="portflio";static \u0275fac=function(r){return new(r||e)};static \u0275cmp=ge({type:e,selectors:[["app-root"]],decls:7,vars:0,consts:[["id","projects"],["id","skills"],["id","experiences"],["id","contact"]],template:function(r,i){r&1&&v(0,"app-header")(1,"router-outlet")(2,"app-projects",0)(3,"app-skills",1)(4,"app-experiences",2)(5,"app-contact",3)(6,"app-footer")},dependencies:[Af,my,vy,Cy,Dy,by,_y],styles:["[_nghost-%COMP%]{display:block;width:100%;max-width:100vw;overflow-x:hidden}"]})}return e})();var My=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=De({type:e,bootstrap:[Iy]});static \u0275inj=we({imports:[Oa,o1,gy,y0,w1,D1]})}return e})();_0().bootstrapModule(My,{ngZoneEventCoalescing:!0}).catch(e=>console.error(e));
